"""
Test Suite for Unified Excel Importer

This script provides comprehensive testing for the unified Excel importer
to ensure all functionality from existing importers is preserved.

Author: Unified Excel Importer System
Date: 2024
"""

import os
import sys
import logging
import tempfile
import pandas as pd
from typing import Dict, Any, List
import unittest
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from unified_excel_importer import (
    UnifiedExcelImporter, 
    import_from_excel,
    SystemCapabilityDetector,
    EnhancedColumnMapper,
    detect_system_capabilities,
    should_use_enhanced_chunked_processing
)
from excel_importer_compatibility import (
    robust_excel_import,
    memory_safe_excel_import,
    enhanced_excel_import,
    flexible_excel_import,
    ImporterMigrationHelper
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestUnifiedExcelImporter(unittest.TestCase):
    """Test cases for the unified Excel importer."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_data_dir = tempfile.mkdtemp()
        self.sample_excel_file = self._create_sample_excel_file()
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)
    
    def _create_sample_excel_file(self) -> str:
        """Create a sample Excel file for testing."""
        file_path = os.path.join(self.test_data_dir, 'test_data.xlsx')
        
        # Create sample data
        equipment_data = {
            'BA Number': ['BA001', 'BA002', 'BA003'],
            'Make and Type': ['Engine Type A', 'Engine Type B', 'Engine Type C'],
            'Serial Number': ['SN001', 'SN002', 'SN003'],
            'Units Held': [1, 2, 1],
            'Vintage Years': [5.5, 3.2, 7.8],
            'Meterage (KMs)': [50000, 30000, 80000],
            'Hours Run Total': [2500, 1800, 4000],
            'Equipment Status': ['active', 'active', 'maintenance']
        }
        
        fluid_data = {
            'BA Number': ['BA001', 'BA002', 'BA003'],
            'Engine Oil Capacity (Ltrs)': [15.5, 12.0, 18.2],
            'Engine Oil Grade': ['15W40', '10W30', '15W40'],
            'Engine Oil Periodicity (KM)': [10000, 8000, 12000],
            'Hydraulic Oil Capacity (Ltrs)': [25.0, 20.0, 30.0],
            'Hydraulic Oil Grade': ['AW46', 'AW32', 'AW46'],
            'Coolant Capacity (Ltrs)': [8.5, 7.0, 9.5]
        }
        
        maintenance_data = {
            'BA Number': ['BA001', 'BA002', 'BA003'],
            'TM-1 Done Date': ['2024-01-15', '2024-02-20', '2024-03-10'],
            'TM-1 Due Date': ['2024-07-15', '2024-08-20', '2024-09-10'],
            'TM-2 Done Date': ['2023-12-01', '2024-01-15', '2023-11-20'],
            'TM-2 Due Date': ['2024-06-01', '2024-07-15', '2024-05-20']
        }
        
        # Create Excel file with multiple sheets
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            pd.DataFrame(equipment_data).to_excel(writer, sheet_name='Equipment', index=False)
            pd.DataFrame(fluid_data).to_excel(writer, sheet_name='Fluids', index=False)
            pd.DataFrame(maintenance_data).to_excel(writer, sheet_name='Maintenance', index=False)
        
        return file_path
    
    def test_system_capability_detection(self):
        """Test system capability detection."""
        logger.info("Testing system capability detection...")
        
        capabilities = SystemCapabilityDetector.detect_capabilities()
        
        self.assertIsNotNone(capabilities)
        self.assertGreater(capabilities.total_memory_gb, 0)
        self.assertGreater(capabilities.cpu_count, 0)
        self.assertIsNotNone(capabilities.recommended_strategy)
        
        logger.info(f"Detected capabilities: {capabilities.total_memory_gb}GB RAM, {capabilities.cpu_count} CPUs")
    
    def test_enhanced_column_mapper(self):
        """Test enhanced column mapping functionality."""
        logger.info("Testing enhanced column mapping...")
        
        mapper = EnhancedColumnMapper()
        
        # Test equipment column mapping
        test_columns = ['BA Number', 'Make and Type', 'Serial Number', 'Units Held']
        mapping = mapper.map_columns(test_columns)
        
        self.assertIn('equipment_fields', mapping)
        self.assertGreater(len(mapping['equipment_fields']), 0)
        
        # Test flexible mapping
        flexible_mapping = mapper.get_flexible_mapping(test_columns)
        self.assertIn('equipment', flexible_mapping)
        self.assertIn('confidence_scores', flexible_mapping)
        
        logger.info(f"Column mapping completed with {len(mapping['equipment_fields'])} equipment fields mapped")
    
    def test_unified_importer_initialization(self):
        """Test unified importer initialization."""
        logger.info("Testing unified importer initialization...")
        
        importer = UnifiedExcelImporter(self.sample_excel_file)
        
        self.assertEqual(importer.file_path, self.sample_excel_file)
        self.assertIsNotNone(importer.capabilities)
        self.assertIsNotNone(importer.memory_manager)
        self.assertIsNotNone(importer.stats)
        
        logger.info("Unified importer initialized successfully")
    
    @patch('unified_excel_importer.models')
    def test_import_functionality(self, mock_models):
        """Test the main import functionality."""
        logger.info("Testing import functionality...")
        
        # Mock database models
        mock_equipment = Mock()
        mock_equipment.save = Mock()
        mock_models.Equipment.return_value = mock_equipment
        
        # Test import
        result = import_from_excel(self.sample_excel_file)
        
        self.assertIsInstance(result, dict)
        self.assertIn('total_processed', result)
        self.assertIn('strategy_used', result)
        self.assertIn('processing_time', result)
        
        logger.info(f"Import completed: {result.get('total_processed', 0)} records processed")
    
    def test_compatibility_functions(self):
        """Test backward compatibility functions."""
        logger.info("Testing compatibility functions...")
        
        # Test robust import compatibility
        with patch('excel_importer_compatibility.import_from_excel') as mock_import:
            mock_import.return_value = {'equipment': 5, 'total_processed': 5}
            result = robust_excel_import(self.sample_excel_file)
            self.assertEqual(result['equipment'], 5)
        
        # Test memory-safe import compatibility
        with patch('excel_importer_compatibility.UnifiedExcelImporter') as mock_importer:
            mock_instance = Mock()
            mock_instance.import_all_data.return_value = {'fluids': 3, 'total_processed': 3}
            mock_importer.return_value = mock_instance
            
            result = memory_safe_excel_import(self.sample_excel_file, chunk_size=500)
            self.assertEqual(result['fluids'], 3)
        
        logger.info("Compatibility functions tested successfully")
    
    def test_migration_helper(self):
        """Test migration helper functionality."""
        logger.info("Testing migration helper...")
        
        helper = ImporterMigrationHelper()
        
        # Test configuration migration
        old_config = {'use_staging': True, 'chunk_size': 1000}
        new_config = helper.migrate_robust_importer_config(old_config)
        
        self.assertIn('enable_staging', new_config)
        self.assertEqual(new_config['enable_staging'], True)
        
        # Test recommendations
        recommendations = helper.get_migration_recommendations("")
        self.assertIsInstance(recommendations, dict)
        self.assertGreater(len(recommendations), 0)
        
        logger.info("Migration helper tested successfully")
    
    def test_error_handling(self):
        """Test error handling and recovery."""
        logger.info("Testing error handling...")
        
        # Test with non-existent file
        result = import_from_excel("non_existent_file.xlsx")
        
        self.assertIsInstance(result, dict)
        self.assertIn('errors', result)
        self.assertGreater(len(result['errors']), 0)
        self.assertEqual(result['total_processed'], 0)
        
        logger.info("Error handling tested successfully")
    
    def test_chunked_processing_detection(self):
        """Test chunked processing detection."""
        logger.info("Testing chunked processing detection...")
        
        # Test with sample file
        should_chunk = should_use_enhanced_chunked_processing(self.sample_excel_file)
        self.assertIsInstance(should_chunk, bool)
        
        logger.info(f"Chunked processing recommended: {should_chunk}")


class TestPerformanceAndMemory(unittest.TestCase):
    """Test performance and memory management."""
    
    def test_memory_management(self):
        """Test memory management functionality."""
        logger.info("Testing memory management...")
        
        from unified_excel_importer import MemoryManager
        
        memory_manager = MemoryManager(threshold_mb=100)
        
        # Test memory monitoring
        current_usage = memory_manager.get_memory_usage_mb()
        self.assertGreater(current_usage, 0)
        
        # Test cleanup decision
        should_cleanup = memory_manager.should_cleanup()
        self.assertIsInstance(should_cleanup, bool)
        
        logger.info(f"Current memory usage: {current_usage:.1f}MB")
    
    def test_processing_strategies(self):
        """Test different processing strategies."""
        logger.info("Testing processing strategies...")
        
        from unified_excel_importer import ProcessingStrategy
        
        # Test strategy enumeration
        strategies = [strategy.value for strategy in ProcessingStrategy]
        expected_strategies = ['high_performance', 'memory_safe', 'chunked_processing', 'compatibility']
        
        for strategy in expected_strategies:
            self.assertIn(strategy, strategies)
        
        logger.info(f"Available strategies: {strategies}")


def run_integration_tests():
    """Run integration tests with real Excel files if available."""
    logger.info("Running integration tests...")
    
    # Look for test Excel files in the project directory
    test_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith(('.xlsx', '.xls')) and 'test' not in file.lower():
                test_files.append(os.path.join(root, file))
                if len(test_files) >= 3:  # Limit to 3 files for testing
                    break
    
    if not test_files:
        logger.info("No Excel files found for integration testing")
        return
    
    logger.info(f"Found {len(test_files)} Excel files for integration testing")
    
    for file_path in test_files:
        try:
            logger.info(f"Testing with file: {os.path.basename(file_path)}")
            
            # Test system capability detection
            capabilities = detect_system_capabilities()
            logger.info(f"System capabilities: {capabilities['recommended_strategy']}")
            
            # Test import
            result = import_from_excel(file_path)
            
            logger.info(f"Import result: {result.get('total_processed', 0)} records, "
                       f"Strategy: {result.get('strategy_used', 'unknown')}, "
                       f"Time: {result.get('processing_time', 0):.2f}s")
            
            if result.get('errors'):
                logger.warning(f"Errors encountered: {len(result['errors'])}")
                for error in result['errors'][:3]:  # Show first 3 errors
                    logger.warning(f"  - {error}")
            
        except Exception as e:
            logger.error(f"Integration test failed for {file_path}: {e}")


def main():
    """Main test runner."""
    print("=" * 80)
    print("UNIFIED EXCEL IMPORTER TEST SUITE")
    print("=" * 80)
    
    # Run unit tests
    print("\n1. Running Unit Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration tests
    print("\n2. Running Integration Tests...")
    run_integration_tests()
    
    # Show system information
    print("\n3. System Information...")
    capabilities = detect_system_capabilities()
    print(f"Total Memory: {capabilities['total_memory_gb']:.1f} GB")
    print(f"Available Memory: {capabilities['available_memory_gb']:.1f} GB")
    print(f"CPU Count: {capabilities['cpu_count']}")
    print(f"Recommended Strategy: {capabilities['recommended_strategy']}")
    
    print("\n" + "=" * 80)
    print("TEST SUITE COMPLETED")
    print("=" * 80)


if __name__ == "__main__":
    main()
