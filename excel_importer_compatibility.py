"""
Excel Importer Compatibility Layer

This module provides backward compatibility functions and migration utilities
to ensure seamless transition from existing Excel importers to the unified solution.

Author: Unified Excel Importer System
Date: 2024
"""

import logging
from typing import Dict, Any, Optional, Callable
from unified_excel_importer import import_from_excel, UnifiedExcelImporter

logger = logging.getLogger(__name__)


# Compatibility functions for existing importers

def robust_excel_import(file_path: str, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Compatibility function for robust_excel_importer_working.py
    
    Args:
        file_path: Path to Excel file
        progress_callback: Optional progress callback function
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for robust Excel import: {file_path}")
    return import_from_excel(file_path, progress_callback)


def memory_safe_excel_import(file_path: str, chunk_size: int = 1000, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for memory_safe_excel_importer.py
    
    Args:
        file_path: Path to Excel file
        chunk_size: Chunk size for processing
        **kwargs: Additional arguments
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for memory-safe Excel import: {file_path}")
    
    # Create importer with chunked processing strategy
    importer = UnifiedExcelImporter(
        file_path=file_path,
        chunk_size=chunk_size,
        force_strategy='chunked',
        **kwargs
    )
    
    return importer.import_all_data()


def enhanced_excel_import(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for enhanced_excel_importer.py
    
    Args:
        file_path: Path to Excel file
        **kwargs: Additional arguments
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for enhanced Excel import: {file_path}")
    return import_from_excel(file_path, **kwargs)


def flexible_excel_import(file_path: str, header_configs: Optional[list] = None, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for flexible_excel_importer.py
    
    Args:
        file_path: Path to Excel file
        header_configs: Header configurations to try
        **kwargs: Additional arguments
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for flexible Excel import: {file_path}")
    
    # The unified importer already handles multiple header configurations
    return import_from_excel(file_path, **kwargs)


def cross_system_excel_import(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for cross_system_excel_importer.py
    
    Args:
        file_path: Path to Excel file
        **kwargs: Additional arguments
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for cross-system Excel import: {file_path}")
    return import_from_excel(file_path, **kwargs)


def column_fix_excel_import(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for excel_importer_column_fix.py
    
    Args:
        file_path: Path to Excel file
        **kwargs: Additional arguments
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for column-fix Excel import: {file_path}")
    return import_from_excel(file_path, **kwargs)


def excel_import_fixes(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for excel_import_fixes.py
    
    Args:
        file_path: Path to Excel file
        **kwargs: Additional arguments
        
    Returns:
        Import results dictionary
    """
    logger.info(f"Using unified importer for Excel import fixes: {file_path}")
    return import_from_excel(file_path, **kwargs)


# Migration utilities

class ImporterMigrationHelper:
    """Helper class for migrating from old importers to unified importer."""
    
    @staticmethod
    def migrate_robust_importer_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate configuration from robust_excel_importer_working.py
        
        Args:
            config: Old importer configuration
            
        Returns:
            Unified importer configuration
        """
        unified_config = {
            'enable_staging': config.get('use_staging', True),
            'progress_callback': config.get('progress_callback'),
            'force_strategy': 'compatibility'  # Use compatibility mode for robust importer
        }
        
        return unified_config
    
    @staticmethod
    def migrate_memory_safe_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate configuration from memory_safe_excel_importer.py
        
        Args:
            config: Old importer configuration
            
        Returns:
            Unified importer configuration
        """
        unified_config = {
            'chunk_size': config.get('chunk_size', 1000),
            'force_strategy': 'chunked',
            'memory_threshold_mb': config.get('memory_limit_mb', 500)
        }
        
        return unified_config
    
    @staticmethod
    def migrate_enhanced_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate configuration from enhanced_excel_importer.py
        
        Args:
            config: Old importer configuration
            
        Returns:
            Unified importer configuration
        """
        unified_config = {
            'enable_staging': config.get('enable_staging', True),
            'force_strategy': 'high_performance',
            'progress_callback': config.get('progress_callback')
        }
        
        return unified_config
    
    @staticmethod
    def get_migration_recommendations(file_path: str) -> Dict[str, str]:
        """
        Get recommendations for migrating specific importer usage.
        
        Args:
            file_path: Path to the file being analyzed
            
        Returns:
            Dictionary with migration recommendations
        """
        recommendations = {
            'robust_excel_importer_working.py': 'Use import_from_excel() with compatibility strategy',
            'memory_safe_excel_importer.py': 'Use UnifiedExcelImporter with chunked strategy',
            'enhanced_excel_importer.py': 'Use import_from_excel() with high-performance strategy',
            'flexible_excel_importer.py': 'Use import_from_excel() - flexibility is built-in',
            'cross_system_excel_importer.py': 'Use import_from_excel() - cross-system support is built-in',
            'excel_importer_column_fix.py': 'Use import_from_excel() - enhanced column mapping is built-in',
            'excel_import_fixes.py': 'Use import_from_excel() - fixes are incorporated'
        }
        
        return recommendations


# Wrapper functions for specific use cases

def import_equipment_data(file_path: str, **kwargs) -> Dict[str, Any]:
    """Import only equipment data from Excel file."""
    result = import_from_excel(file_path, **kwargs)
    return {
        'equipment': result.get('equipment', 0),
        'equipment_updated': result.get('equipment_updated', 0),
        'errors': result.get('errors', []),
        'warnings': result.get('warnings', [])
    }


def import_fluid_data(file_path: str, **kwargs) -> Dict[str, Any]:
    """Import only fluid data from Excel file."""
    result = import_from_excel(file_path, **kwargs)
    return {
        'fluids': result.get('fluids', 0),
        'errors': result.get('errors', []),
        'warnings': result.get('warnings', [])
    }


def import_maintenance_data(file_path: str, **kwargs) -> Dict[str, Any]:
    """Import only maintenance data from Excel file."""
    result = import_from_excel(file_path, **kwargs)
    return {
        'maintenance': result.get('maintenance', 0),
        'errors': result.get('errors', []),
        'warnings': result.get('warnings', [])
    }


def import_forecast_data(file_path: str, **kwargs) -> Dict[str, Any]:
    """Import only forecast data from Excel file."""
    result = import_from_excel(file_path, **kwargs)
    return {
        'demand_forecast': result.get('demand_forecast', 0),
        'tyre_forecast': result.get('tyre_forecast', 0),
        'battery_forecast': result.get('battery_forecast', 0),
        'equipment_forecast': result.get('equipment_forecast', 0),
        'overhaul_forecast': result.get('overhaul_forecast', 0),
        'conditioning_forecast': result.get('conditioning_forecast', 0),
        'errors': result.get('errors', []),
        'warnings': result.get('warnings', [])
    }


# Legacy function aliases for backward compatibility

# Aliases for robust_excel_importer_working.py
process_excel_file = robust_excel_import
import_excel_data = robust_excel_import

# Aliases for memory_safe_excel_importer.py
import_excel_chunked = memory_safe_excel_import
process_excel_chunked = memory_safe_excel_import

# Aliases for enhanced_excel_importer.py
import_excel_enhanced = enhanced_excel_import
process_excel_enhanced = enhanced_excel_import

# Aliases for flexible_excel_importer.py
import_excel_flexible = flexible_excel_import
process_excel_flexible = flexible_excel_import

# General aliases
import_from_excel_file = import_from_excel
process_excel_import = import_from_excel
excel_data_import = import_from_excel


if __name__ == "__main__":
    # Test compatibility functions
    import sys
    
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
        print(f"Testing compatibility layer with file: {test_file}")
        
        # Test different compatibility functions
        print("Testing robust import compatibility...")
        result1 = robust_excel_import(test_file)
        print(f"Robust import result: {result1.get('total_processed', 0)} records")
        
        print("Testing memory-safe import compatibility...")
        result2 = memory_safe_excel_import(test_file, chunk_size=500)
        print(f"Memory-safe import result: {result2.get('total_processed', 0)} records")
        
        print("Testing enhanced import compatibility...")
        result3 = enhanced_excel_import(test_file)
        print(f"Enhanced import result: {result3.get('total_processed', 0)} records")
        
    else:
        print("Usage: python excel_importer_compatibility.py <excel_file_path>")
        
        # Show migration recommendations
        helper = ImporterMigrationHelper()
        recommendations = helper.get_migration_recommendations("")
        print("\nMigration Recommendations:")
        for old_importer, recommendation in recommendations.items():
            print(f"  {old_importer}: {recommendation}")
