"""
Unified Excel Importer for PROJECT-ALPHA
Consolidates all Excel import functionality into a single, comprehensive solution.

This module combines the best features from all existing Excel importers:
- robust_excel_importer_working.py: Core database integration and entity processing
- excel_import_fixes.py: Enhanced memory management and system detection
- memory_safe_excel_importer.py: Chunked processing and memory safety
- cross_system_excel_importer.py: Multi-strategy processing and compatibility
- enhanced_excel_importer.py: Deployment compatibility and error handling
- flexible_excel_importer.py: Format flexibility and header handling
- excel_importer_column_fix.py: Advanced column mapping and fuzzy matching

Author: Unified Excel Importer System
Version: 1.0.0
"""

import os
import gc
import re
import logging
import sqlite3
import psutil
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Tuple, Optional, Any, Iterator, Union
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import traceback
import json

# Import configuration and database modules
import config
import database
import models
from dateutil import parser as date_parser

logger = logging.getLogger('unified_excel_importer')

class ProcessingStrategy(Enum):
    """Available processing strategies based on system capabilities."""
    HIGH_PERFORMANCE = "high_performance"
    MEMORY_SAFE = "memory_safe"
    CHUNKED_PROCESSING = "chunked_processing"
    COMPATIBILITY = "compatibility"

@dataclass
class SystemCapabilities:
    """System capability information for strategy selection."""
    total_memory_gb: float
    available_memory_gb: float
    cpu_count: int
    disk_space_gb: float
    python_version: str
    pandas_version: str
    recommended_strategy: ProcessingStrategy

@dataclass
class ImportStats:
    """Comprehensive import statistics."""
    equipment: int = 0
    fluids: int = 0
    maintenance: int = 0
    repairs: int = 0
    overhauls: int = 0
    batteries: int = 0
    tyre_maintenance: int = 0
    discard_criteria: int = 0
    medium_resets: int = 0
    conditioning: int = 0
    demand_forecast: int = 0
    tyre_forecast: int = 0
    battery_forecast: int = 0
    equipment_forecast: int = 0
    overhaul_forecast: int = 0
    conditioning_forecast: int = 0
    equipment_updated: int = 0
    total_processed: int = 0
    errors: List[str] = None
    warnings: List[str] = None
    processing_time: float = 0.0
    memory_peak_mb: float = 0.0
    strategy_used: str = ""
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert stats to dictionary format for compatibility."""
        return {
            'equipment': self.equipment,
            'fluids': self.fluids,
            'maintenance': self.maintenance,
            'repairs': self.repairs,
            'overhauls': self.overhauls,
            'batteries': self.batteries,
            'tyre_maintenance': self.tyre_maintenance,
            'discard_criteria': self.discard_criteria,
            'medium_resets': self.medium_resets,
            'conditioning': self.conditioning,
            'demand_forecast': self.demand_forecast,
            'tyre_forecast': self.tyre_forecast,
            'battery_forecast': self.battery_forecast,
            'equipment_forecast': self.equipment_forecast,
            'overhaul_forecast': self.overhaul_forecast,
            'conditioning_forecast': self.conditioning_forecast,
            'equipment_updated': self.equipment_updated,
            'total_processed': self.total_processed,
            'errors': self.errors,
            'warnings': self.warnings,
            'processing_time': self.processing_time,
            'memory_peak_mb': self.memory_peak_mb,
            'strategy_used': self.strategy_used
        }

class SystemCapabilityDetector:
    """Detects system capabilities and recommends processing strategy."""
    
    @staticmethod
    def detect_capabilities() -> SystemCapabilities:
        """Detect current system capabilities."""
        try:
            # Memory information
            memory = psutil.virtual_memory()
            total_memory_gb = memory.total / (1024**3)
            available_memory_gb = memory.available / (1024**3)
            
            # CPU information
            cpu_count = psutil.cpu_count()
            
            # Disk space
            disk_usage = psutil.disk_usage('/')
            disk_space_gb = disk_usage.free / (1024**3)
            
            # Python and pandas versions
            import sys
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            pandas_version = pd.__version__
            
            # Recommend strategy based on capabilities
            if total_memory_gb >= 8 and cpu_count >= 4:
                recommended_strategy = ProcessingStrategy.HIGH_PERFORMANCE
            elif total_memory_gb >= 4:
                recommended_strategy = ProcessingStrategy.MEMORY_SAFE
            elif total_memory_gb >= 2:
                recommended_strategy = ProcessingStrategy.CHUNKED_PROCESSING
            else:
                recommended_strategy = ProcessingStrategy.COMPATIBILITY
            
            return SystemCapabilities(
                total_memory_gb=total_memory_gb,
                available_memory_gb=available_memory_gb,
                cpu_count=cpu_count,
                disk_space_gb=disk_space_gb,
                python_version=python_version,
                pandas_version=pandas_version,
                recommended_strategy=recommended_strategy
            )
            
        except Exception as e:
            logger.warning(f"Could not detect system capabilities: {e}")
            # Return conservative defaults
            return SystemCapabilities(
                total_memory_gb=2.0,
                available_memory_gb=1.0,
                cpu_count=2,
                disk_space_gb=10.0,
                python_version="3.8.0",
                pandas_version="1.0.0",
                recommended_strategy=ProcessingStrategy.COMPATIBILITY
            )

class MemoryManager:
    """Manages memory usage and cleanup during processing."""
    
    def __init__(self, max_memory_mb: int = 512):
        self.max_memory_mb = max_memory_mb
        self.peak_memory_mb = 0.0
    
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / (1024 * 1024)
            self.peak_memory_mb = max(self.peak_memory_mb, memory_mb)
            return memory_mb
        except Exception:
            return 0.0
    
    def should_cleanup(self) -> bool:
        """Check if memory cleanup is needed."""
        return self.get_memory_usage_mb() > self.max_memory_mb
    
    def force_cleanup(self):
        """Force garbage collection and memory cleanup."""
        try:
            gc.collect()
            logger.debug(f"Memory cleanup performed. Current usage: {self.get_memory_usage_mb():.1f}MB")
        except Exception as e:
            logger.warning(f"Memory cleanup failed: {e}")

class UnifiedExcelImporter:
    """
    Unified Excel Importer that consolidates all existing Excel import functionality.
    
    Features:
    - Automatic system capability detection and strategy selection
    - Memory-safe processing with configurable limits
    - Advanced column mapping with fuzzy matching
    - Comprehensive entity support (Equipment, Fluids, Maintenance, etc.)
    - Robust error handling and recovery mechanisms
    - Progress tracking and detailed statistics
    - Cross-system compatibility
    """
    
    def __init__(self, 
                 file_path: str,
                 strategy: Optional[ProcessingStrategy] = None,
                 chunk_size: int = 1000,
                 max_memory_mb: int = 512,
                 enable_staging: bool = True,
                 progress_callback: Optional[callable] = None):
        """
        Initialize the Unified Excel Importer.
        
        Args:
            file_path: Path to the Excel file to import
            strategy: Processing strategy (auto-detected if None)
            chunk_size: Number of rows to process at once for chunked processing
            max_memory_mb: Maximum memory usage before cleanup
            enable_staging: Whether to use staging database for conflict resolution
            progress_callback: Optional callback for progress updates
        """
        self.file_path = file_path
        self.chunk_size = chunk_size
        self.enable_staging = enable_staging
        self.progress_callback = progress_callback
        
        # Initialize components
        self.capabilities = SystemCapabilityDetector.detect_capabilities()
        self.strategy = strategy or self.capabilities.recommended_strategy
        self.memory_manager = MemoryManager(max_memory_mb)
        self.stats = ImportStats()
        
        # Processing state
        self.excel_file = None
        self.staging_initialized = False
        
        logger.info(f"Unified Excel Importer initialized for {file_path}")
        logger.info(f"System capabilities: {self.capabilities.total_memory_gb:.1f}GB RAM, {self.capabilities.cpu_count} CPUs")
        logger.info(f"Selected strategy: {self.strategy.value}")

    def import_all_data(self) -> Dict[str, Any]:
        """
        Main entry point for importing all data from Excel file.

        Returns:
            Dictionary with import statistics and results
        """
        start_time = datetime.now()
        self.stats.strategy_used = self.strategy.value

        try:
            logger.info(f"Starting unified Excel import from {self.file_path}")
            self._update_progress(0, "Initializing import...")

            # Validate file exists and is readable
            if not self._validate_file():
                return self.stats.to_dict()

            # Initialize staging database if enabled
            if self.enable_staging and not self._initialize_staging():
                logger.warning("Staging initialization failed, continuing without staging")
                self.enable_staging = False

            # Execute import based on selected strategy
            self._update_progress(10, "Analyzing file structure...")

            if self.strategy == ProcessingStrategy.HIGH_PERFORMANCE:
                result = self._import_high_performance()
            elif self.strategy == ProcessingStrategy.MEMORY_SAFE:
                result = self._import_memory_safe()
            elif self.strategy == ProcessingStrategy.CHUNKED_PROCESSING:
                result = self._import_chunked()
            else:  # COMPATIBILITY
                result = self._import_compatibility()

            # Calculate final statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            self.stats.processing_time = processing_time
            self.stats.memory_peak_mb = self.memory_manager.peak_memory_mb
            self.stats.total_processed = sum([
                self.stats.equipment, self.stats.fluids, self.stats.maintenance,
                self.stats.repairs, self.stats.overhauls, self.stats.batteries,
                self.stats.tyre_maintenance, self.stats.discard_criteria,
                self.stats.medium_resets, self.stats.conditioning,
                self.stats.demand_forecast, self.stats.tyre_forecast,
                self.stats.battery_forecast, self.stats.equipment_forecast,
                self.stats.overhaul_forecast, self.stats.conditioning_forecast
            ])

            self._update_progress(100, "Import completed successfully!")

            logger.info(f"Unified Excel import completed in {processing_time:.2f}s")
            logger.info(f"Total records processed: {self.stats.total_processed}")
            logger.info(f"Peak memory usage: {self.stats.memory_peak_mb:.1f}MB")

            return self.stats.to_dict()

        except Exception as e:
            error_msg = f"Unified Excel import failed: {e}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.stats.errors.append(error_msg)

            # Calculate partial statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            self.stats.processing_time = processing_time
            self.stats.memory_peak_mb = self.memory_manager.peak_memory_mb

            return self.stats.to_dict()

        finally:
            # Cleanup resources
            self._cleanup_resources()

    def _validate_file(self) -> bool:
        """Validate that the Excel file exists and is readable."""
        try:
            if not os.path.exists(self.file_path):
                error_msg = f"Excel file not found: {self.file_path}"
                logger.error(error_msg)
                self.stats.errors.append(error_msg)
                return False

            # Try to open the file to verify it's a valid Excel file
            try:
                test_file = pd.ExcelFile(self.file_path)
                if not test_file.sheet_names:
                    error_msg = f"Excel file has no sheets: {self.file_path}"
                    logger.error(error_msg)
                    self.stats.errors.append(error_msg)
                    return False
                test_file.close()
            except Exception as e:
                error_msg = f"Invalid Excel file format: {e}"
                logger.error(error_msg)
                self.stats.errors.append(error_msg)
                return False

            logger.info(f"File validation successful: {self.file_path}")
            return True

        except Exception as e:
            error_msg = f"File validation error: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _initialize_staging(self) -> bool:
        """Initialize staging database for conflict resolution."""
        try:
            # Import and use the robust importer's staging initialization
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()

            if temp_importer.initialize_staging():
                self.staging_initialized = True
                logger.info("Staging database initialized successfully")
                return True
            else:
                logger.warning("Failed to initialize staging database")
                return False

        except Exception as e:
            logger.warning(f"Staging initialization error: {e}")
            return False

    def _update_progress(self, percentage: int, message: str):
        """Update progress if callback is provided."""
        if self.progress_callback:
            try:
                self.progress_callback(percentage, message)
            except Exception as e:
                logger.warning(f"Progress callback error: {e}")

        logger.debug(f"Progress: {percentage}% - {message}")

    def _cleanup_resources(self):
        """Clean up resources and perform final memory cleanup."""
        try:
            if self.excel_file:
                self.excel_file.close()
                self.excel_file = None

            # Force final memory cleanup
            self.memory_manager.force_cleanup()

            logger.debug("Resource cleanup completed")

        except Exception as e:
            logger.warning(f"Resource cleanup error: {e}")

class EnhancedColumnMapper:
    """
    Advanced column mapping with fuzzy matching and flexible detection.
    Consolidates all column mapping logic from existing importers.
    """

    def __init__(self):
        # Standard column patterns for different entity types
        self.equipment_patterns = {
            'ba_number': [
                r'ba\s*no', r'ba\s*number', r'battalion', r'ba\s*num',
                r'b\.a\.\s*no', r'b\.a\.\s*number'
            ],
            'serial_number': [
                r'ser\s*no', r'serial', r's\s*no', r'sl\s*no', r'ser\s*num',
                r'serial\s*number', r'serial\s*no'
            ],
            'make_and_type': [
                r'make\s*&\s*type', r'make\s*and\s*type', r'make/type',
                r'equipment', r'make\s*type', r'type', r'model'
            ],
            'vintage_years': [
                r'vintage', r'age', r'years', r'vintage\s*years',
                r'age\s*years', r'equipment\s*age'
            ],
            'meterage_kms': [
                r'km\s*run', r'meterage', r'mileage', r'kms', r'kilometers',
                r'km\s*total', r'total\s*km', r'odometer'
            ],
            'hours_run_total': [
                r'hours\s*run', r'total\s*hours', r'hrs\s*run', r'engine\s*hours',
                r'operating\s*hours', r'run\s*hours'
            ],
            'date_of_commission': [
                r'date\s*of\s*commission', r'commission\s*date', r'commissioned',
                r'induction\s*date', r'service\s*date'
            ]
        }

        self.fluid_patterns = {
            'engine_oil': [
                r'eng\s*oil', r'engine\s*oil', r'motor\s*oil', r'lub\s*oil',
                r'lubrication\s*oil', r'crankcase\s*oil'
            ],
            'hydraulic_fluid': [
                r'hyd\s*fluid', r'hydraulic\s*fluid', r'hydraulic\s*oil',
                r'hyd\s*oil', r'power\s*steering'
            ],
            'coolant': [
                r'coolant', r'radiator\s*fluid', r'antifreeze', r'cooling\s*fluid',
                r'rad\s*fluid'
            ],
            'transmission_oil': [
                r'trans\s*oil', r'transmission\s*oil', r'gear\s*oil',
                r'gearbox\s*oil', r'trans\s*fluid'
            ],
            'brake_fluid': [
                r'brake\s*fluid', r'brake\s*oil', r'braking\s*fluid'
            ]
        }

        self.maintenance_patterns = {
            'tm1': [r'tm\s*1', r'tm-1', r'tm1', r'maintenance\s*1'],
            'tm2': [r'tm\s*2', r'tm-2', r'tm2', r'maintenance\s*2'],
            'tm3': [r'tm\s*3', r'tm-3', r'tm3', r'maintenance\s*3'],
            'service': [r'service', r'servicing', r'maintenance'],
            'inspection': [r'inspection', r'check', r'examine']
        }

        self.overhaul_patterns = {
            'oh1': [r'oh\s*1', r'oh-1', r'oh1', r'overhaul\s*1', r'first\s*oh'],
            'oh2': [r'oh\s*2', r'oh-2', r'oh2', r'overhaul\s*2', r'second\s*oh'],
            'major_oh': [r'major\s*oh', r'major\s*overhaul', r'complete\s*oh'],
            'minor_oh': [r'minor\s*oh', r'minor\s*overhaul', r'partial\s*oh']
        }

    def map_columns(self, columns: List[str]) -> Dict[str, Any]:
        """
        Enhanced column mapping with comprehensive pattern matching.

        Args:
            columns: List of column names from Excel sheet

        Returns:
            Dictionary with mapped columns and metadata
        """
        logger.info(f"Starting enhanced column mapping for {len(columns)} columns")

        # Clean and normalize columns
        cleaned_columns = self._clean_columns(columns)

        # Map different entity types
        equipment_mapping = self._map_entity_columns(cleaned_columns, self.equipment_patterns)
        fluid_mapping = self._map_fluid_columns(cleaned_columns)
        maintenance_mapping = self._map_entity_columns(cleaned_columns, self.maintenance_patterns)
        overhaul_mapping = self._map_entity_columns(cleaned_columns, self.overhaul_patterns)

        # Calculate mapping confidence
        total_patterns = (len(self.equipment_patterns) + len(self.fluid_patterns) +
                         len(self.maintenance_patterns) + len(self.overhaul_patterns))
        total_mapped = (len(equipment_mapping) + len(fluid_mapping) +
                       len(maintenance_mapping) + len(overhaul_mapping))
        confidence = (total_mapped / total_patterns) * 100 if total_patterns > 0 else 0

        result = {
            'equipment_fields': equipment_mapping,
            'fluid_fields': fluid_mapping,
            'maintenance_fields': maintenance_mapping,
            'overhaul_fields': overhaul_mapping,
            'total_columns': len(columns),
            'mapped_columns': total_mapped,
            'confidence': confidence,
            'cleaned_columns': cleaned_columns
        }

        logger.info(f"Column mapping completed: {total_mapped} fields mapped with {confidence:.1f}% confidence")
        return result

    def _clean_columns(self, columns: List[str]) -> List[str]:
        """Clean and normalize column names."""
        cleaned = []
        for col in columns:
            if col is None:
                continue

            col_str = str(col).strip()

            # Skip problematic columns
            if (col_str.startswith('Unnamed') or
                col_str.lower() in ['', 'nan', 'none', 'null'] or
                len(col_str) < 2):
                continue

            # Clean whitespace and line breaks
            col_str = re.sub(r'\s+', ' ', col_str)
            col_str = col_str.replace('\n', ' ').replace('\r', ' ')

            cleaned.append(col_str)

        return cleaned

    def _map_entity_columns(self, columns: List[str], patterns: Dict[str, List[str]]) -> Dict[str, str]:
        """Map columns using pattern matching for a specific entity type."""
        mapping = {}

        for field, pattern_list in patterns.items():
            best_match = None
            best_score = 0

            for col in columns:
                score = self._calculate_match_score(col, pattern_list)
                if score > best_score and score > 0.6:  # Minimum confidence threshold
                    best_match = col
                    best_score = score

            if best_match:
                mapping[field] = best_match
                logger.debug(f"Mapped '{field}' to '{best_match}' (score: {best_score:.2f})")

        return mapping

    def _map_fluid_columns(self, columns: List[str]) -> Dict[str, Dict[str, str]]:
        """Map fluid-related columns with capacity and periodicity detection."""
        fluid_mapping = {}

        for fluid_type, patterns in self.fluid_patterns.items():
            fluid_data = {}

            # Find base fluid column
            base_col = None
            best_score = 0

            for col in columns:
                score = self._calculate_match_score(col, patterns)
                if score > best_score and score > 0.6:
                    base_col = col
                    best_score = score

            if base_col:
                fluid_data['base_column'] = base_col

                # Look for related columns (capacity, grade, etc.)
                for col in columns:
                    col_lower = col.lower()
                    if any(pattern.replace(r'\s*', ' ') in col_lower for pattern in patterns):
                        if 'capacity' in col_lower or 'ltrs' in col_lower or 'ltr' in col_lower:
                            fluid_data['capacity'] = col
                        elif 'grade' in col_lower or 'type' in col_lower:
                            fluid_data['grade'] = col
                        elif 'period' in col_lower or 'km' in col_lower:
                            fluid_data['periodicity'] = col

                if fluid_data:
                    fluid_mapping[fluid_type] = fluid_data

        return fluid_mapping

    def _calculate_match_score(self, column: str, patterns: List[str]) -> float:
        """Calculate match score between column name and patterns."""
        if not column or not patterns:
            return 0.0

        col_lower = column.lower()
        max_score = 0.0

        for pattern in patterns:
            try:
                # Direct regex match
                if re.search(pattern, col_lower, re.IGNORECASE):
                    max_score = max(max_score, 1.0)
                    continue

                # Fuzzy matching - check if pattern words are in column
                pattern_words = re.sub(r'[^\w\s]', ' ', pattern).split()
                col_words = re.sub(r'[^\w\s]', ' ', col_lower).split()

                if pattern_words and col_words:
                    matches = sum(1 for pw in pattern_words if any(pw in cw for cw in col_words))
                    score = matches / len(pattern_words)
                    max_score = max(max_score, score)

            except re.error:
                # If regex fails, try simple string matching
                pattern_clean = re.sub(r'[^\w\s]', ' ', pattern).lower()
                if pattern_clean in col_lower:
                    max_score = max(max_score, 0.8)

        return max_score

    def find_column_matches(self, columns: List[str], field: str) -> List[Tuple[str, float]]:
        """Find all column matches for a field with confidence scores."""
        matches = []

        # Get patterns for the field
        all_patterns = {**self.equipment_patterns, **self.fluid_patterns,
                       **self.maintenance_patterns, **self.overhaul_patterns}

        patterns = all_patterns.get(field, [])
        if not patterns:
            return matches

        for col in columns:
            score = self._calculate_match_score(col, patterns)
            if score > 0.5:  # Minimum threshold
                matches.append((col, score))

        # Sort by confidence score (highest first)
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches

    def get_flexible_mapping(self, columns: List[str]) -> Dict[str, Any]:
        """Get flexible column mapping with enhanced detection."""
        logger.info("Performing flexible column mapping")

        # Clean columns first
        cleaned_columns = self._clean_columns(columns)

        # Create comprehensive mapping
        mapping_result = {
            'equipment': {},
            'fluids': {},
            'maintenance': {},
            'overhauls': {},
            'batteries': {},
            'tyres': {},
            'forecasts': {},
            'unmapped_columns': [],
            'confidence_scores': {}
        }

        # Map equipment fields
        for field, patterns in self.equipment_patterns.items():
            matches = self.find_column_matches(cleaned_columns, field)
            if matches:
                best_match, confidence = matches[0]
                mapping_result['equipment'][field] = best_match
                mapping_result['confidence_scores'][field] = confidence

        # Map fluid fields with enhanced detection
        fluid_mapping = self._detect_fluid_columns_advanced(cleaned_columns)
        mapping_result['fluids'] = fluid_mapping

        # Map maintenance fields
        for field, patterns in self.maintenance_patterns.items():
            matches = self.find_column_matches(cleaned_columns, field)
            if matches:
                best_match, confidence = matches[0]
                mapping_result['maintenance'][field] = best_match
                mapping_result['confidence_scores'][field] = confidence

        # Map overhaul fields
        for field, patterns in self.overhaul_patterns.items():
            matches = self.find_column_matches(cleaned_columns, field)
            if matches:
                best_match, confidence = matches[0]
                mapping_result['overhauls'][field] = best_match
                mapping_result['confidence_scores'][field] = confidence

        # Detect battery columns
        mapping_result['batteries'] = self._detect_battery_columns(cleaned_columns)

        # Detect tyre columns
        mapping_result['tyres'] = self._detect_tyre_columns(cleaned_columns)

        # Detect forecast columns
        mapping_result['forecasts'] = self._detect_forecast_columns(cleaned_columns)

        # Find unmapped columns
        all_mapped = set()
        for category in ['equipment', 'maintenance', 'overhauls']:
            all_mapped.update(mapping_result[category].values())

        for fluid_data in mapping_result['fluids'].values():
            if isinstance(fluid_data, dict):
                all_mapped.update(fluid_data.values())

        mapping_result['unmapped_columns'] = [col for col in cleaned_columns if col not in all_mapped]

        # Calculate overall confidence
        total_confidence = sum(mapping_result['confidence_scores'].values())
        avg_confidence = total_confidence / len(mapping_result['confidence_scores']) if mapping_result['confidence_scores'] else 0
        mapping_result['overall_confidence'] = avg_confidence

        logger.info(f"Flexible mapping completed with {avg_confidence:.1f}% confidence")
        return mapping_result

    def _detect_fluid_columns_advanced(self, columns: List[str]) -> Dict[str, Dict[str, str]]:
        """Advanced fluid column detection with capacity and periodicity."""
        fluid_mapping = {}

        for fluid_type, patterns in self.fluid_patterns.items():
            fluid_data = {}

            # Find base fluid column
            matches = []
            for col in columns:
                score = self._calculate_match_score(col, patterns)
                if score > 0.6:
                    matches.append((col, score))

            if matches:
                # Get best match
                best_match = max(matches, key=lambda x: x[1])
                fluid_data['base_column'] = best_match[0]

                # Look for related columns near the base column
                base_index = columns.index(best_match[0])
                search_range = range(max(0, base_index - 3), min(len(columns), base_index + 4))

                for i in search_range:
                    col = columns[i]
                    col_lower = col.lower()

                    # Detect capacity columns
                    if any(term in col_lower for term in ['capacity', 'ltrs', 'ltr', 'litre', 'kg']):
                        fluid_data['capacity'] = col

                    # Detect grade columns
                    elif any(term in col_lower for term in ['grade', 'type', 'spec', 'viscosity']):
                        fluid_data['grade'] = col

                    # Detect periodicity columns
                    elif any(term in col_lower for term in ['period', 'km', 'hrs', 'hours', 'months']):
                        if 'km' in col_lower:
                            fluid_data['periodicity_km'] = col
                        elif any(term in col_lower for term in ['hrs', 'hours']):
                            fluid_data['periodicity_hrs'] = col
                        elif 'month' in col_lower:
                            fluid_data['periodicity_months'] = col

                    # Detect service date columns
                    elif any(term in col_lower for term in ['service', 'change', 'last', 'date']):
                        fluid_data['last_serviced_date'] = col

                if fluid_data:
                    fluid_mapping[fluid_type] = fluid_data

        return fluid_mapping

    def _detect_battery_columns(self, columns: List[str]) -> Dict[str, str]:
        """Detect battery-related columns."""
        battery_mapping = {}
        battery_patterns = {
            'battery_type': [r'battery\s*type', r'batt\s*type', r'cell\s*type'],
            'battery_capacity': [r'battery\s*capacity', r'batt\s*capacity', r'ah', r'amp\s*hour'],
            'battery_voltage': [r'voltage', r'volt', r'v'],
            'battery_date_of_change': [r'battery\s*change', r'batt\s*change', r'replacement\s*date']
        }

        for field, patterns in battery_patterns.items():
            matches = self.find_column_matches(columns, field)
            if matches:
                battery_mapping[field] = matches[0][0]

        return battery_mapping

    def _detect_tyre_columns(self, columns: List[str]) -> Dict[str, str]:
        """Detect tyre-related columns."""
        tyre_mapping = {}
        tyre_patterns = {
            'tyre_rotation_kms': [r'tyre\s*rotation', r'tire\s*rotation', r'rotation\s*km'],
            'tyre_condition_kms': [r'tyre\s*condition', r'tire\s*condition', r'condition\s*km'],
            'tyre_condition_years': [r'tyre\s*years', r'tire\s*years', r'condition\s*years'],
            'last_rotation_date': [r'last\s*rotation', r'rotation\s*date']
        }

        for field, patterns in tyre_patterns.items():
            for col in columns:
                score = self._calculate_match_score(col, patterns)
                if score > 0.6:
                    tyre_mapping[field] = col
                    break

        return tyre_mapping

    def _detect_forecast_columns(self, columns: List[str]) -> Dict[str, str]:
        """Detect forecast-related columns."""
        forecast_mapping = {}
        forecast_patterns = {
            'fiscal_year': [r'fiscal\s*year', r'fy', r'year', r'financial\s*year'],
            'total_requirement': [r'total\s*requirement', r'requirement', r'demand', r'forecast'],
            'conditioning_type': [r'conditioning', r'condition\s*type', r'treatment'],
            'replacement_reason': [r'replacement\s*reason', r'reason', r'cause']
        }

        for field, patterns in forecast_patterns.items():
            for col in columns:
                score = self._calculate_match_score(col, patterns)
                if score > 0.6:
                    forecast_mapping[field] = col
                    break

        return forecast_mapping

    # Add the processing strategy methods to UnifiedExcelImporter class
    def _import_high_performance(self) -> Dict[str, Any]:
        """High-performance import strategy for systems with abundant resources."""
        logger.info("Using high-performance import strategy")
        self._update_progress(20, "Loading Excel file...")

        try:
            # Load entire file into memory for fast processing
            self.excel_file = pd.ExcelFile(self.file_path)

            self._update_progress(30, "Processing all sheets...")

            # Process all sheets simultaneously
            for sheet_name in self.excel_file.sheet_names:
                logger.info(f"Processing sheet: {sheet_name}")

                try:
                    # Try multiple header configurations
                    df = self._read_sheet_robust(sheet_name)
                    if df is None or df.empty:
                        continue

                    # Map columns for this sheet
                    column_mapping = EnhancedColumnMapper().map_columns(df.columns.tolist())

                    # Process all entity types for this sheet
                    self._process_sheet_data(df, sheet_name, column_mapping)

                except Exception as e:
                    error_msg = f"Error processing sheet {sheet_name}: {e}"
                    logger.error(error_msg)
                    self.stats.errors.append(error_msg)
                    continue

            self._update_progress(90, "Finalizing high-performance import...")
            return self.stats.to_dict()

        except Exception as e:
            error_msg = f"High-performance import failed: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            # Fallback to memory-safe strategy
            logger.info("Falling back to memory-safe strategy")
            return self._import_memory_safe()

    def _import_memory_safe(self) -> Dict[str, Any]:
        """Memory-safe import strategy with controlled resource usage."""
        logger.info("Using memory-safe import strategy")
        self._update_progress(20, "Opening Excel file with memory constraints...")

        try:
            self.excel_file = pd.ExcelFile(self.file_path)

            for sheet_name in self.excel_file.sheet_names:
                logger.info(f"Processing sheet: {sheet_name}")
                self._update_progress(30 + (40 * len([s for s in self.excel_file.sheet_names if s <= sheet_name]) / len(self.excel_file.sheet_names)),
                                    f"Processing sheet: {sheet_name}")

                try:
                    # Read sheet with memory optimization
                    df = self._read_sheet_robust(sheet_name)
                    if df is None or df.empty:
                        continue

                    # Check memory usage before processing
                    if self.memory_manager.should_cleanup():
                        logger.info("Memory threshold reached, performing cleanup")
                        self.memory_manager.force_cleanup()

                    # Map columns
                    column_mapping = EnhancedColumnMapper().map_columns(df.columns.tolist())

                    # Process sheet data
                    self._process_sheet_data(df, sheet_name, column_mapping)

                    # Clear dataframe from memory
                    del df
                    self.memory_manager.force_cleanup()

                except Exception as e:
                    error_msg = f"Error processing sheet {sheet_name}: {e}"
                    logger.error(error_msg)
                    self.stats.errors.append(error_msg)
                    continue

            self._update_progress(90, "Finalizing memory-safe import...")
            return self.stats.to_dict()

        except Exception as e:
            error_msg = f"Memory-safe import failed: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            # Fallback to chunked processing
            logger.info("Falling back to chunked processing strategy")
            return self._import_chunked()

    def _import_chunked(self) -> Dict[str, Any]:
        """Chunked processing strategy for large files or limited memory."""
        logger.info("Using chunked processing import strategy")
        self._update_progress(20, "Initializing chunked processing...")

        try:
            self.excel_file = pd.ExcelFile(self.file_path)

            for sheet_name in self.excel_file.sheet_names:
                logger.info(f"Processing sheet in chunks: {sheet_name}")

                try:
                    # Process sheet in chunks
                    chunk_count = 0
                    for chunk_df in self._read_sheet_chunked(sheet_name):
                        chunk_count += 1
                        logger.debug(f"Processing chunk {chunk_count} from sheet {sheet_name}")

                        # Map columns (use first chunk for mapping)
                        if chunk_count == 1:
                            column_mapping = EnhancedColumnMapper().map_columns(chunk_df.columns.tolist())

                        # Process chunk data
                        self._process_sheet_data(chunk_df, sheet_name, column_mapping)

                        # Memory cleanup after each chunk
                        del chunk_df
                        if self.memory_manager.should_cleanup():
                            self.memory_manager.force_cleanup()

                        # Update progress
                        progress = 30 + (50 * chunk_count / 10)  # Estimate progress
                        self._update_progress(min(int(progress), 80), f"Processing chunk {chunk_count} of {sheet_name}")

                except Exception as e:
                    error_msg = f"Error processing chunked sheet {sheet_name}: {e}"
                    logger.error(error_msg)
                    self.stats.errors.append(error_msg)
                    continue

            self._update_progress(90, "Finalizing chunked import...")
            return self.stats.to_dict()

        except Exception as e:
            error_msg = f"Chunked import failed: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            # Fallback to compatibility strategy
            logger.info("Falling back to compatibility strategy")
            return self._import_compatibility()

    def _import_compatibility(self) -> Dict[str, Any]:
        """Compatibility strategy for maximum system compatibility."""
        logger.info("Using compatibility import strategy")
        self._update_progress(20, "Using compatibility mode...")

        try:
            # Use the most basic and compatible approach
            # Similar to the original robust_excel_importer_working.py
            from robust_excel_importer_working import RobustExcelImporter

            # Create and configure robust importer
            robust_importer = RobustExcelImporter()

            if self.enable_staging and not robust_importer.initialize_staging():
                logger.warning("Staging initialization failed in compatibility mode")

            self._update_progress(40, "Processing with robust importer...")

            # Use the robust importer's process_excel_file method
            success, result = robust_importer.process_excel_file(self.file_path)

            if success and isinstance(result, dict):
                # Convert robust importer results to our stats format
                self.stats.equipment = result.get('equipment', 0)
                self.stats.fluids = result.get('fluids', 0)
                self.stats.maintenance = result.get('maintenance', 0)
                self.stats.repairs = result.get('repairs', 0)
                self.stats.overhauls = result.get('overhauls', 0)
                self.stats.batteries = result.get('batteries', 0)
                self.stats.tyre_maintenance = result.get('tyre_maintenance', 0)
                self.stats.discard_criteria = result.get('discard_criteria', 0)
                self.stats.equipment_updated = result.get('equipment_updated', 0)

                if 'errors' in result:
                    self.stats.errors.extend(result['errors'])

                logger.info("Compatibility import completed successfully")
            else:
                error_msg = f"Robust importer failed: {result}"
                logger.error(error_msg)
                self.stats.errors.append(error_msg)

            self._update_progress(90, "Finalizing compatibility import...")
            return self.stats.to_dict()

        except Exception as e:
            error_msg = f"Compatibility import failed: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return self.stats.to_dict()

    def _read_sheet_robust(self, sheet_name: str) -> Optional[pd.DataFrame]:
        """Read Excel sheet with multiple header configuration attempts."""
        if not self.excel_file:
            return None

        # Try different header configurations (from flexible_excel_importer.py)
        header_configs = [[0, 1, 2], [0, 1], [0], None]

        for header_config in header_configs:
            try:
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name, header=header_config)

                if df.empty:
                    continue

                # Handle multi-level headers
                if header_config and len(header_config) > 1 and hasattr(df, 'columns') and isinstance(df.columns, pd.MultiIndex):
                    # Flatten multi-level columns
                    new_columns = []
                    for col in df.columns:
                        if isinstance(col, tuple):
                            # Join non-null parts
                            parts = [str(part) for part in col if str(part) not in ['nan', 'Unnamed', 'None']]
                            new_columns.append(' -> '.join(parts) if parts else 'Unnamed')
                        else:
                            new_columns.append(str(col))
                    df.columns = new_columns

                # Clean column names
                df.columns = [str(col).strip() for col in df.columns]

                logger.debug(f"Successfully read sheet {sheet_name} with header config {header_config}")
                return df

            except Exception as e:
                logger.debug(f"Failed to read sheet {sheet_name} with header config {header_config}: {e}")
                continue

        logger.warning(f"Could not read sheet {sheet_name} with any header configuration")
        return None

    def _read_sheet_chunked(self, sheet_name: str) -> Iterator[pd.DataFrame]:
        """Read Excel sheet in chunks for memory-efficient processing."""
        if not self.excel_file:
            return

        try:
            # Get total rows to estimate chunks
            temp_df = pd.read_excel(self.excel_file, sheet_name=sheet_name, nrows=0)
            column_names = temp_df.columns.tolist()

            chunk_start = 0
            while True:
                try:
                    chunk_df = pd.read_excel(
                        self.excel_file,
                        sheet_name=sheet_name,
                        skiprows=chunk_start if chunk_start > 0 else None,
                        nrows=self.chunk_size,
                        header=0 if chunk_start == 0 else None
                    )

                    if chunk_df.empty:
                        break

                    # Use original column names for subsequent chunks
                    if chunk_start > 0:
                        chunk_df.columns = column_names[:len(chunk_df.columns)]

                    yield chunk_df
                    chunk_start += self.chunk_size

                except Exception as e:
                    logger.error(f"Error reading chunk starting at row {chunk_start}: {e}")
                    break

        except Exception as e:
            logger.error(f"Error setting up chunked reading for sheet {sheet_name}: {e}")

    def _process_sheet_data(self, df: pd.DataFrame, sheet_name: str, column_mapping: Dict[str, Any]):
        """Process data from a sheet using the column mapping."""
        if df.empty:
            return

        logger.info(f"Processing {len(df)} rows from sheet {sheet_name}")

        try:
            # Process equipment data
            equipment_count = self._process_equipment_data(df, sheet_name, column_mapping.get('equipment_fields', {}))
            self.stats.equipment += equipment_count

            # Process fluid data
            fluid_count = self._process_fluid_data(df, sheet_name, column_mapping.get('fluid_fields', {}))
            self.stats.fluids += fluid_count

            # Process maintenance data
            maintenance_count = self._process_maintenance_data(df, sheet_name, column_mapping.get('maintenance_fields', {}))
            self.stats.maintenance += maintenance_count

            # Process overhaul data
            overhaul_count = self._process_overhaul_data(df, sheet_name, column_mapping.get('overhaul_fields', {}))
            self.stats.overhauls += overhaul_count

            # Process other entity types (batteries, tyres, forecasts, etc.)
            self._process_other_entities(df, sheet_name, column_mapping)

            logger.info(f"Sheet {sheet_name} processed: {equipment_count} equipment, {fluid_count} fluids, {maintenance_count} maintenance, {overhaul_count} overhauls")

        except Exception as e:
            error_msg = f"Error processing sheet data for {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)

    def _process_equipment_data(self, df: pd.DataFrame, sheet_name: str, equipment_mapping: Dict[str, str]) -> int:
        """Process equipment data from the dataframe."""
        if not equipment_mapping:
            logger.debug(f"No equipment mapping found for sheet {sheet_name}")
            return 0

        equipment_count = 0
        updated_count = 0

        try:
            for index, row in df.iterrows():
                try:
                    # Extract equipment data using mapping
                    equipment_data = self._extract_equipment_data(row, equipment_mapping)

                    if not equipment_data or not equipment_data.get('make_and_type'):
                        continue

                    # Check if equipment already exists (BA Number matching)
                    existing_id = self._find_equipment_by_ba_number(equipment_data.get('ba_number'))

                    if existing_id:
                        # Update existing equipment
                        if self._update_existing_equipment(existing_id, equipment_data, sheet_name):
                            updated_count += 1
                    else:
                        # Create new equipment
                        if self._create_new_equipment(equipment_data, sheet_name):
                            equipment_count += 1

                except Exception as e:
                    logger.error(f"Error processing equipment row {index} in {sheet_name}: {e}")
                    continue

            self.stats.equipment_updated += updated_count
            logger.info(f"Equipment processing completed: {equipment_count} new, {updated_count} updated")
            return equipment_count

        except Exception as e:
            error_msg = f"Equipment processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return 0

    def _extract_equipment_data(self, row: pd.Series, mapping: Dict[str, str]) -> Dict[str, Any]:
        """Extract equipment data from a row using the column mapping."""
        equipment_data = {}

        # Define field types for proper parsing
        numeric_fields = ['vintage_years', 'meterage_kms', 'hours_run_total', 'units_held']
        date_fields = ['date_of_commission', 'date_of_induction']

        for field, column in mapping.items():
            if column in row.index:
                value = row[column]

                # Skip empty or invalid values
                if pd.isna(value) or str(value).strip() in ['', '-', 'NA', 'N/A', 'nan', 'NaN']:
                    continue

                # Parse based on field type
                if field in numeric_fields:
                    equipment_data[field] = self._parse_numeric(value)
                elif field in date_fields:
                    equipment_data[field] = self._parse_date(value)
                else:
                    equipment_data[field] = str(value).strip()

        return equipment_data

    def _parse_numeric(self, value: Any) -> Optional[float]:
        """Parse numeric value with robust error handling."""
        if pd.isna(value):
            return None

        try:
            # Handle string values
            if isinstance(value, str):
                # Remove common non-numeric characters
                cleaned = re.sub(r'[^\d.-]', '', value.strip())
                if not cleaned:
                    return None
                return float(cleaned)

            # Handle numeric values
            return float(value)

        except (ValueError, TypeError):
            logger.debug(f"Could not parse numeric value: {value}")
            return None

    def _parse_date(self, value: Any) -> Optional[str]:
        """Parse date value with multiple format support."""
        if pd.isna(value) or str(value).strip() in ['', '-', 'NA', 'N/A']:
            return None

        try:
            # Handle pandas datetime
            if isinstance(value, pd.Timestamp):
                return value.strftime('%Y-%m-%d')

            # Handle datetime objects
            if isinstance(value, (datetime, date)):
                return value.strftime('%Y-%m-%d')

            # Handle string dates
            if isinstance(value, str):
                # Try common date formats
                for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%Y/%m/%d']:
                    try:
                        parsed_date = datetime.strptime(value.strip(), fmt)
                        return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        continue

                # Try dateutil parser as fallback
                try:
                    parsed_date = date_parser.parse(value.strip())
                    return parsed_date.strftime('%Y-%m-%d')
                except (ValueError, TypeError):
                    pass

            # Handle Excel date numbers
            if isinstance(value, (int, float)):
                try:
                    # Excel date serial number
                    excel_date = pd.to_datetime(value, origin='1899-12-30', unit='D')
                    return excel_date.strftime('%Y-%m-%d')
                except (ValueError, TypeError):
                    pass

            logger.debug(f"Could not parse date value: {value}")
            return None

        except Exception as e:
            logger.debug(f"Date parsing error for value {value}: {e}")
            return None

    def _find_equipment_by_ba_number(self, ba_number: str) -> Optional[int]:
        """Find existing equipment by BA Number."""
        if not ba_number:
            return None

        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT equipment_id FROM equipment WHERE ba_number = ? AND is_active = 1",
                (str(ba_number).strip(),)
            )

            result = cursor.fetchone()
            conn.close()

            return result[0] if result else None

        except Exception as e:
            logger.error(f"Error finding equipment by BA Number {ba_number}: {e}")
            return None

    def _create_new_equipment(self, equipment_data: Dict[str, Any], sheet_name: str) -> bool:
        """Create new equipment record in database."""
        try:
            # Create Equipment model instance
            equipment = models.Equipment(
                serial_number=equipment_data.get('serial_number'),
                make_and_type=equipment_data.get('make_and_type'),
                ba_number=equipment_data.get('ba_number'),
                units_held=equipment_data.get('units_held', 1),
                vintage_years=equipment_data.get('vintage_years', 0.0),
                meterage_kms=equipment_data.get('meterage_kms', 0.0),
                hours_run_total=equipment_data.get('hours_run_total', 0.0),
                date_of_commission=equipment_data.get('date_of_commission'),
                date_of_induction=equipment_data.get('date_of_induction'),
                equipment_status=equipment_data.get('equipment_status', 'active'),
                remarks=equipment_data.get('remarks'),
                is_active=True
            )

            # Save to database
            equipment.save()

            logger.debug(f"Created new equipment: {equipment_data.get('make_and_type')} (BA: {equipment_data.get('ba_number')})")
            return True

        except Exception as e:
            error_msg = f"Error creating equipment from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _update_existing_equipment(self, equipment_id: int, equipment_data: Dict[str, Any], sheet_name: str) -> bool:
        """Update existing equipment record."""
        try:
            # Load existing equipment
            equipment = models.Equipment.get_by_id(equipment_id)
            if not equipment:
                return False

            # Update fields with new data
            for field, value in equipment_data.items():
                if hasattr(equipment, field) and value is not None:
                    setattr(equipment, field, value)

            # Save updated equipment
            equipment.save()

            logger.debug(f"Updated equipment ID {equipment_id}: {equipment_data.get('make_and_type')}")
            return True

        except Exception as e:
            error_msg = f"Error updating equipment ID {equipment_id} from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _process_fluid_data(self, df: pd.DataFrame, sheet_name: str, fluid_mapping: Dict[str, Dict[str, str]]) -> int:
        """Process fluid data from the dataframe."""
        if not fluid_mapping:
            logger.debug(f"No fluid mapping found for sheet {sheet_name}")
            return 0

        fluid_count = 0

        try:
            # Get equipment IDs for fluid association
            equipment_ids = self._get_equipment_ids_from_sheet(df)

            for fluid_type, mapping in fluid_mapping.items():
                base_column = mapping.get('base_column')
                if not base_column:
                    continue

                for index, row in df.iterrows():
                    try:
                        # Check if this row has fluid data
                        if pd.isna(row.get(base_column)):
                            continue

                        # Extract fluid data
                        fluid_data = self._extract_fluid_data(row, mapping, fluid_type)
                        if not fluid_data:
                            continue

                        # Associate with equipment if possible
                        equipment_id = equipment_ids.get(index) if equipment_ids else None
                        if equipment_id:
                            fluid_data['equipment_id'] = equipment_id

                        # Create fluid record
                        if self._create_fluid_record(fluid_data, sheet_name):
                            fluid_count += 1

                    except Exception as e:
                        logger.error(f"Error processing fluid row {index} in {sheet_name}: {e}")
                        continue

            logger.info(f"Fluid processing completed: {fluid_count} records created")
            return fluid_count

        except Exception as e:
            error_msg = f"Fluid processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return 0

    def _extract_fluid_data(self, row: pd.Series, mapping: Dict[str, str], fluid_type: str) -> Dict[str, Any]:
        """Extract fluid data from a row using the mapping."""
        fluid_data = {'fluid_type': fluid_type}

        # Map capacity data
        if 'capacity' in mapping and mapping['capacity'] in row.index:
            capacity_value = self._parse_numeric(row[mapping['capacity']])
            if capacity_value:
                fluid_data['capacity_ltrs_kg'] = capacity_value

        # Map grade data
        if 'grade' in mapping and mapping['grade'] in row.index:
            grade_value = row[mapping['grade']]
            if pd.notna(grade_value) and str(grade_value).strip():
                fluid_data['grade'] = str(grade_value).strip()

        # Map periodicity data
        if 'periodicity' in mapping and mapping['periodicity'] in row.index:
            period_value = self._parse_numeric(row[mapping['periodicity']])
            if period_value:
                fluid_data['periodicity_km'] = int(period_value)

        return fluid_data if len(fluid_data) > 1 else {}

    def _create_fluid_record(self, fluid_data: Dict[str, Any], sheet_name: str) -> bool:
        """Create fluid record in database."""
        try:
            fluid = models.Fluid(
                equipment_id=fluid_data.get('equipment_id'),
                fluid_type=fluid_data.get('fluid_type'),
                capacity_ltrs_kg=fluid_data.get('capacity_ltrs_kg', 0.0),
                grade=fluid_data.get('grade'),
                periodicity_km=fluid_data.get('periodicity_km', 0),
                periodicity_hrs=fluid_data.get('periodicity_hrs', 0),
                periodicity_months=fluid_data.get('periodicity_months', 0)
            )

            fluid.save()
            return True

        except Exception as e:
            error_msg = f"Error creating fluid record from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _process_maintenance_data(self, df: pd.DataFrame, sheet_name: str, maintenance_mapping: Dict[str, str]) -> int:
        """Process maintenance data from the dataframe."""
        if not maintenance_mapping:
            return 0

        maintenance_count = 0

        try:
            # Get equipment IDs for maintenance association
            equipment_ids = self._get_equipment_ids_from_sheet(df)

            for index, row in df.iterrows():
                try:
                    # Extract maintenance data
                    maintenance_data = self._extract_maintenance_data(row, maintenance_mapping)
                    if not maintenance_data:
                        continue

                    # Associate with equipment
                    equipment_id = equipment_ids.get(index)
                    if equipment_id:
                        maintenance_data['equipment_id'] = equipment_id

                        # Create maintenance record
                        if self._create_maintenance_record(maintenance_data, sheet_name):
                            maintenance_count += 1

                except Exception as e:
                    logger.error(f"Error processing maintenance row {index} in {sheet_name}: {e}")
                    continue

            return maintenance_count

        except Exception as e:
            error_msg = f"Maintenance processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return 0

    def _extract_maintenance_data(self, row: pd.Series, mapping: Dict[str, str]) -> Dict[str, Any]:
        """Extract maintenance data from a row."""
        maintenance_data = {}

        # Define field types
        date_fields = ['done_date', 'due_date', 'next_due_date', 'actual_completion_date']
        numeric_fields = ['vintage_years', 'meterage_kms', 'completion_meterage']

        for field, column in mapping.items():
            if column in row.index:
                value = row[column]

                if pd.isna(value) or str(value).strip() in ['', '-', 'NA', 'N/A']:
                    continue

                if field in date_fields:
                    maintenance_data[field] = self._parse_date(value)
                elif field in numeric_fields:
                    maintenance_data[field] = self._parse_numeric(value)
                else:
                    maintenance_data[field] = str(value).strip()

        return maintenance_data

    def _create_maintenance_record(self, maintenance_data: Dict[str, Any], sheet_name: str) -> bool:
        """Create maintenance record in database."""
        try:
            maintenance = models.Maintenance(
                equipment_id=maintenance_data.get('equipment_id'),
                maintenance_type=maintenance_data.get('maintenance_type'),
                done_date=maintenance_data.get('done_date'),
                next_due_date=maintenance_data.get('next_due_date'),
                vintage_years=maintenance_data.get('vintage_years', 0.0),
                meterage_kms=maintenance_data.get('meterage_kms', 0.0),
                completion_notes=maintenance_data.get('completion_notes'),
                status=maintenance_data.get('status', 'scheduled'),
                completed_by=maintenance_data.get('completed_by'),
                actual_completion_date=maintenance_data.get('actual_completion_date'),
                completion_meterage=maintenance_data.get('completion_meterage'),
                maintenance_category=maintenance_data.get('maintenance_category', 'TM-1')
            )

            maintenance.save()
            return True

        except Exception as e:
            error_msg = f"Error creating maintenance record from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _process_overhaul_data(self, df: pd.DataFrame, sheet_name: str, overhaul_mapping: Dict[str, str]) -> int:
        """Process overhaul data from the dataframe."""
        if not overhaul_mapping:
            return 0

        overhaul_count = 0

        try:
            # Get equipment IDs for overhaul association
            equipment_ids = self._get_equipment_ids_from_sheet(df)

            for index, row in df.iterrows():
                try:
                    # Extract overhaul data for different types
                    for overhaul_type in ['oh1', 'oh2', 'major_oh', 'minor_oh']:
                        if overhaul_type in overhaul_mapping:
                            overhaul_data = self._extract_overhaul_data(row, overhaul_mapping, overhaul_type)
                            if overhaul_data:
                                equipment_id = equipment_ids.get(index)
                                if equipment_id:
                                    overhaul_data['equipment_id'] = equipment_id

                                    if self._create_overhaul_record(overhaul_data, sheet_name):
                                        overhaul_count += 1

                except Exception as e:
                    logger.error(f"Error processing overhaul row {index} in {sheet_name}: {e}")
                    continue

            return overhaul_count

        except Exception as e:
            error_msg = f"Overhaul processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return 0

    def _extract_overhaul_data(self, row: pd.Series, mapping: Dict[str, str], overhaul_type: str) -> Dict[str, Any]:
        """Extract overhaul data from a row."""
        overhaul_data = {'overhaul_type': overhaul_type}

        # Look for overhaul-specific columns
        type_column = mapping.get(overhaul_type)
        if type_column and type_column in row.index:
            value = row[type_column]
            if pd.notna(value) and str(value).strip():
                # Check for done date
                done_date = self._parse_date(value)
                if done_date:
                    overhaul_data['overhaul_done_date'] = done_date

                # Look for due date in nearby columns
                due_column = type_column.replace('done', 'due').replace('DONE', 'DUE')
                if due_column in row.index:
                    due_value = row[due_column]
                    due_date = self._parse_date(due_value)
                    if due_date:
                        overhaul_data['overhaul_due_date'] = due_date

        return overhaul_data if len(overhaul_data) > 1 else {}

    def _create_overhaul_record(self, overhaul_data: Dict[str, Any], sheet_name: str) -> bool:
        """Create overhaul record in database."""
        try:
            overhaul = models.Overhaul(
                equipment_id=overhaul_data.get('equipment_id'),
                overhaul_type=overhaul_data.get('overhaul_type'),
                overhaul_done_date=overhaul_data.get('overhaul_done_date'),
                overhaul_due_date=overhaul_data.get('overhaul_due_date')
            )

            overhaul.save()
            return True

        except Exception as e:
            error_msg = f"Error creating overhaul record from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _process_other_entities(self, df: pd.DataFrame, sheet_name: str, column_mapping: Dict[str, Any]):
        """Process other entity types (batteries, tyres, forecasts, etc.)."""
        try:
            # Process battery data
            if 'batteries' in column_mapping and column_mapping['batteries']:
                battery_count = self._process_battery_data(df, sheet_name, column_mapping['batteries'])
                self.stats.batteries += battery_count

            # Process tyre data
            if 'tyres' in column_mapping and column_mapping['tyres']:
                tyre_count = self._process_tyre_data(df, sheet_name, column_mapping['tyres'])
                self.stats.tyre_maintenance += tyre_count

            # Process forecast data
            if 'forecasts' in column_mapping and column_mapping['forecasts']:
                forecast_counts = self._process_forecast_data(df, sheet_name, column_mapping['forecasts'])
                self.stats.demand_forecast += forecast_counts.get('demand', 0)
                self.stats.tyre_forecast += forecast_counts.get('tyre', 0)
                self.stats.battery_forecast += forecast_counts.get('battery', 0)
                self.stats.equipment_forecast += forecast_counts.get('equipment', 0)
                self.stats.overhaul_forecast += forecast_counts.get('overhaul', 0)
                self.stats.conditioning_forecast += forecast_counts.get('conditioning', 0)

        except Exception as e:
            error_msg = f"Error processing other entities for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)

    def _get_equipment_ids_from_sheet(self, df: pd.DataFrame) -> Dict[int, int]:
        """Get equipment IDs mapped to row indices for fluid association."""
        equipment_ids = {}

        try:
            # Look for BA Number column to map equipment
            ba_columns = [col for col in df.columns if 'ba' in col.lower() and 'no' in col.lower()]

            if ba_columns:
                ba_column = ba_columns[0]
                for index, row in df.iterrows():
                    ba_number = row.get(ba_column)
                    if pd.notna(ba_number) and str(ba_number).strip():
                        equipment_id = self._find_equipment_by_ba_number(str(ba_number).strip())
                        if equipment_id:
                            equipment_ids[index] = equipment_id

            return equipment_ids

        except Exception as e:
            logger.error(f"Error mapping equipment IDs: {e}")
            return {}

    def _process_battery_data(self, df: pd.DataFrame, sheet_name: str, battery_mapping: Dict[str, str]) -> int:
        """Process battery data from the dataframe."""
        if not battery_mapping:
            return 0

        battery_count = 0

        try:
            equipment_ids = self._get_equipment_ids_from_sheet(df)

            for index, row in df.iterrows():
                try:
                    battery_data = self._extract_battery_data(row, battery_mapping)
                    if not battery_data:
                        continue

                    equipment_id = equipment_ids.get(index)
                    if equipment_id:
                        battery_data['equipment_id'] = equipment_id

                        if self._create_battery_record(battery_data, sheet_name):
                            battery_count += 1

                except Exception as e:
                    logger.error(f"Error processing battery row {index} in {sheet_name}: {e}")
                    continue

            return battery_count

        except Exception as e:
            error_msg = f"Battery processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return 0

    def _extract_battery_data(self, row: pd.Series, mapping: Dict[str, str]) -> Dict[str, Any]:
        """Extract battery data from a row."""
        battery_data = {}

        numeric_fields = ['battery_capacity', 'battery_voltage']
        date_fields = ['battery_date_of_change']

        for field, column in mapping.items():
            if column in row.index:
                value = row[column]

                if pd.isna(value) or str(value).strip() in ['', '-', 'NA', 'N/A']:
                    continue

                if field in numeric_fields:
                    battery_data[field] = self._parse_numeric(value)
                elif field in date_fields:
                    battery_data[field] = self._parse_date(value)
                else:
                    battery_data[field] = str(value).strip()

        return battery_data

    def _create_battery_record(self, battery_data: Dict[str, Any], sheet_name: str) -> bool:
        """Create battery record in database."""
        try:
            battery = models.Battery(
                equipment_id=battery_data.get('equipment_id'),
                battery_type=battery_data.get('battery_type'),
                battery_capacity=battery_data.get('battery_capacity'),
                battery_voltage=battery_data.get('battery_voltage'),
                battery_date_of_change=battery_data.get('battery_date_of_change')
            )

            battery.save()
            return True

        except Exception as e:
            error_msg = f"Error creating battery record from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _process_tyre_data(self, df: pd.DataFrame, sheet_name: str, tyre_mapping: Dict[str, str]) -> int:
        """Process tyre maintenance data from the dataframe."""
        if not tyre_mapping:
            return 0

        tyre_count = 0

        try:
            equipment_ids = self._get_equipment_ids_from_sheet(df)

            for index, row in df.iterrows():
                try:
                    tyre_data = self._extract_tyre_data(row, tyre_mapping)
                    if not tyre_data:
                        continue

                    equipment_id = equipment_ids.get(index)
                    if equipment_id:
                        tyre_data['equipment_id'] = equipment_id

                        if self._create_tyre_record(tyre_data, sheet_name):
                            tyre_count += 1

                except Exception as e:
                    logger.error(f"Error processing tyre row {index} in {sheet_name}: {e}")
                    continue

            return tyre_count

        except Exception as e:
            error_msg = f"Tyre processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return 0

    def _extract_tyre_data(self, row: pd.Series, mapping: Dict[str, str]) -> Dict[str, Any]:
        """Extract tyre data from a row."""
        tyre_data = {}

        numeric_fields = ['tyre_rotation_kms', 'tyre_condition_kms', 'tyre_condition_years']
        date_fields = ['last_rotation_date', 'date_of_change']

        for field, column in mapping.items():
            if column in row.index:
                value = row[column]

                if pd.isna(value) or str(value).strip() in ['', '-', 'NA', 'N/A']:
                    continue

                if field in numeric_fields:
                    tyre_data[field] = self._parse_numeric(value)
                elif field in date_fields:
                    tyre_data[field] = self._parse_date(value)
                else:
                    tyre_data[field] = str(value).strip()

        return tyre_data

    def _create_tyre_record(self, tyre_data: Dict[str, Any], sheet_name: str) -> bool:
        """Create tyre maintenance record in database."""
        try:
            tyre = models.TyreMaintenance(
                equipment_id=tyre_data.get('equipment_id'),
                tyre_rotation_kms=tyre_data.get('tyre_rotation_kms'),
                tyre_condition_kms=tyre_data.get('tyre_condition_kms'),
                tyre_condition_years=tyre_data.get('tyre_condition_years'),
                last_rotation_date=tyre_data.get('last_rotation_date'),
                date_of_change=tyre_data.get('date_of_change')
            )

            tyre.save()
            return True

        except Exception as e:
            error_msg = f"Error creating tyre record from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False

    def _process_forecast_data(self, df: pd.DataFrame, sheet_name: str, forecast_mapping: Dict[str, str]) -> Dict[str, int]:
        """Process forecast data from the dataframe."""
        forecast_counts = {
            'demand': 0, 'tyre': 0, 'battery': 0, 'equipment': 0,
            'overhaul': 0, 'conditioning': 0
        }

        if not forecast_mapping:
            return forecast_counts

        try:
            # Determine forecast type based on sheet name or data content
            forecast_type = self._determine_forecast_type(sheet_name, df, forecast_mapping)

            for index, row in df.iterrows():
                try:
                    forecast_data = self._extract_forecast_data(row, forecast_mapping, forecast_type)
                    if not forecast_data:
                        continue

                    if self._create_forecast_record(forecast_data, forecast_type, sheet_name):
                        forecast_counts[forecast_type] += 1

                except Exception as e:
                    logger.error(f"Error processing forecast row {index} in {sheet_name}: {e}")
                    continue

            return forecast_counts

        except Exception as e:
            error_msg = f"Forecast processing failed for sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return forecast_counts

    def _determine_forecast_type(self, sheet_name: str, df: pd.DataFrame, forecast_mapping: Dict[str, str]) -> str:
        """Determine the type of forecast based on sheet name and content."""
        sheet_lower = sheet_name.lower()

        # Check sheet name for forecast type indicators
        if 'demand' in sheet_lower:
            return 'demand'
        elif 'tyre' in sheet_lower or 'tire' in sheet_lower:
            return 'tyre'
        elif 'battery' in sheet_lower:
            return 'battery'
        elif 'equipment' in sheet_lower:
            return 'equipment'
        elif 'overhaul' in sheet_lower:
            return 'overhaul'
        elif 'conditioning' in sheet_lower:
            return 'conditioning'

        # Check column names for forecast type indicators
        columns_str = ' '.join(df.columns).lower()
        if 'demand' in columns_str:
            return 'demand'
        elif 'tyre' in columns_str or 'tire' in columns_str:
            return 'tyre'
        elif 'battery' in columns_str:
            return 'battery'
        elif 'overhaul' in columns_str:
            return 'overhaul'
        elif 'conditioning' in columns_str:
            return 'conditioning'

        # Default to demand forecast
        return 'demand'

    def _extract_forecast_data(self, row: pd.Series, mapping: Dict[str, str], forecast_type: str) -> Dict[str, Any]:
        """Extract forecast data from a row."""
        forecast_data = {'forecast_type': forecast_type}

        numeric_fields = ['total_requirement', 'quantity', 'cost']
        date_fields = ['forecast_date', 'due_date']

        for field, column in mapping.items():
            if column in row.index:
                value = row[column]

                if pd.isna(value) or str(value).strip() in ['', '-', 'NA', 'N/A']:
                    continue

                if field in numeric_fields:
                    forecast_data[field] = self._parse_numeric(value)
                elif field in date_fields:
                    forecast_data[field] = self._parse_date(value)
                else:
                    forecast_data[field] = str(value).strip()

        return forecast_data if len(forecast_data) > 1 else {}

    def _create_forecast_record(self, forecast_data: Dict[str, Any], forecast_type: str, sheet_name: str) -> bool:
        """Create forecast record in database based on type."""
        try:
            # Create appropriate forecast model based on type
            if forecast_type == 'demand':
                forecast = models.DemandForecast(
                    fiscal_year=forecast_data.get('fiscal_year'),
                    total_requirement=forecast_data.get('total_requirement', 0),
                    forecast_date=forecast_data.get('forecast_date')
                )
            elif forecast_type == 'tyre':
                forecast = models.TyreForecast(
                    fiscal_year=forecast_data.get('fiscal_year'),
                    total_requirement=forecast_data.get('total_requirement', 0),
                    forecast_date=forecast_data.get('forecast_date')
                )
            elif forecast_type == 'battery':
                forecast = models.BatteryForecast(
                    fiscal_year=forecast_data.get('fiscal_year'),
                    total_requirement=forecast_data.get('total_requirement', 0),
                    forecast_date=forecast_data.get('forecast_date')
                )
            elif forecast_type == 'equipment':
                forecast = models.EquipmentForecast(
                    fiscal_year=forecast_data.get('fiscal_year'),
                    total_requirement=forecast_data.get('total_requirement', 0),
                    forecast_date=forecast_data.get('forecast_date')
                )
            elif forecast_type == 'overhaul':
                forecast = models.OverhaulForecast(
                    fiscal_year=forecast_data.get('fiscal_year'),
                    total_requirement=forecast_data.get('total_requirement', 0),
                    forecast_date=forecast_data.get('forecast_date')
                )
            elif forecast_type == 'conditioning':
                forecast = models.ConditioningForecast(
                    fiscal_year=forecast_data.get('fiscal_year'),
                    conditioning_type=forecast_data.get('conditioning_type'),
                    total_requirement=forecast_data.get('total_requirement', 0),
                    forecast_date=forecast_data.get('forecast_date')
                )
            else:
                logger.warning(f"Unknown forecast type: {forecast_type}")
                return False

            forecast.save()
            return True

        except Exception as e:
            error_msg = f"Error creating {forecast_type} forecast record from sheet {sheet_name}: {e}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False


# Main entry point functions for compatibility with existing code

def import_from_excel(file_path: str, progress_callback: Optional[callable] = None) -> Dict[str, Any]:
    """
    Main entry point for Excel import - maintains compatibility with existing code.

    Args:
        file_path: Path to the Excel file to import
        progress_callback: Optional callback for progress updates

    Returns:
        Dictionary with import statistics
    """
    logger.info(f"Starting unified Excel import from {file_path}")

    try:
        # Create unified importer instance
        importer = UnifiedExcelImporter(
            file_path=file_path,
            progress_callback=progress_callback
        )

        # Perform import
        result = importer.import_all_data()

        logger.info(f"Unified Excel import completed: {result.get('total_processed', 0)} records processed")
        return result

    except Exception as e:
        error_msg = f"Unified Excel import failed: {e}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")

        return {
            'equipment': 0, 'fluids': 0, 'maintenance': 0, 'overhauls': 0,
            'batteries': 0, 'tyre_maintenance': 0, 'repairs': 0, 'discard_criteria': 0,
            'medium_resets': 0, 'conditioning': 0, 'demand_forecast': 0,
            'tyre_forecast': 0, 'battery_forecast': 0, 'equipment_forecast': 0,
            'overhaul_forecast': 0, 'conditioning_forecast': 0, 'equipment_updated': 0,
            'total_processed': 0, 'errors': [error_msg], 'warnings': [],
            'processing_time': 0.0, 'memory_peak_mb': 0.0, 'strategy_used': 'failed'
        }


def import_excel_enhanced(file_path: str, **kwargs) -> Dict[str, Any]:
    """Enhanced Excel import function for compatibility."""
    return import_from_excel(file_path, **kwargs)


def import_from_excel_enhanced(file_path: str, **kwargs) -> Dict[str, Any]:
    """Enhanced Excel import function for compatibility."""
    return import_from_excel(file_path, **kwargs)


def import_all_sheets_from_excel(file_path: str) -> Dict[str, Any]:
    """Import all sheets from Excel workbook - compatibility function."""
    return import_from_excel(file_path)


# Utility functions for system capability detection and strategy selection

def detect_system_capabilities() -> Dict[str, Any]:
    """Detect system capabilities and return as dictionary."""
    capabilities = SystemCapabilityDetector.detect_capabilities()
    return {
        'total_memory_gb': capabilities.total_memory_gb,
        'available_memory_gb': capabilities.available_memory_gb,
        'cpu_count': capabilities.cpu_count,
        'disk_space_gb': capabilities.disk_space_gb,
        'python_version': capabilities.python_version,
        'pandas_version': capabilities.pandas_version,
        'recommended_strategy': capabilities.recommended_strategy.value
    }


def should_use_enhanced_chunked_processing(file_path: str) -> bool:
    """Determine if enhanced chunked processing should be used."""
    try:
        # Check file size
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

        # Check system capabilities
        capabilities = SystemCapabilityDetector.detect_capabilities()

        # Use chunked processing for large files or limited memory systems
        return (file_size_mb > 50 or
                capabilities.total_memory_gb < 4 or
                capabilities.recommended_strategy in [ProcessingStrategy.CHUNKED_PROCESSING, ProcessingStrategy.COMPATIBILITY])

    except Exception as e:
        logger.warning(f"Could not determine processing strategy: {e}")
        return True  # Default to chunked processing for safety


if __name__ == "__main__":
    # Test the unified importer
    import sys

    if len(sys.argv) > 1:
        test_file = sys.argv[1]
        print(f"Testing unified Excel importer with file: {test_file}")

        # Set up logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # Test import
        result = import_from_excel(test_file)
        print(f"Import result: {json.dumps(result, indent=2)}")
    else:
        print("Usage: python unified_excel_importer.py <excel_file_path>")

        # Show system capabilities
        capabilities = detect_system_capabilities()
        print(f"System capabilities: {json.dumps(capabilities, indent=2)}")
