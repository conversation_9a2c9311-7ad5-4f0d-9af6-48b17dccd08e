"""
Equipment Inventory Management Application
Main entry point for the application.
"""
import sys
import os
import logging
import platform

# Add project root to Python's import path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set high DPI scaling attribute before QApplication is created
from PyQt5.QtCore import Qt, QCoreApplication
QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)    
# Enhanced DB initialization: directory creation, corruption detection, user notification
from database_ready import ensure_db_ready
from PyQt5.QtWidgets import QApplication, QMessageBox

# Database initialization will be handled in main() function
# Remove duplicate QApplication creation

from datetime import datetime

from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, 
                           QVBoxLayout, QWidget, QSplashScreen, 
                           QStatusBar, QMessageBox, QLabel, QAction, 
                           QFileDialog, QProgressDialog, QInputDialog, QDialog)
from PyQt5.QtGui import QPixmap, QIcon

# Robust resource loader for PyInstaller and dev
import sys

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    if relative_path is None:
        return None
    try:
        base_path = getattr(sys, '_MEIPASS', None)
        if base_path is None:
            raise AttributeError
    except AttributeError:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

from PyQt5.QtCore import Qt, QTimer, QCoreApplication

import database
import config
import utils
# Import unified Excel importer with backward compatibility
from unified_excel_importer import import_from_excel
from excel_importer_compatibility import robust_excel_import
# Import directly from models.py instead of the models package
import models  # Import the module
# Create references to the classes we need
Equipment = models.Equipment
DemandForecast = models.DemandForecast 
TyreMaintenance = models.TyreMaintenance
Fluid = models.Fluid
BatteryForecast = models.BatteryForecast
TyreForecast = models.TyreForecast
from ui.dashboard_widget import DashboardWidget

# Import logging for application
import logging
from ui.equipment_widget import EquipmentWidget
from ui.fluids_widget import FluidsWidget
from ui.maintenance_widget import MaintenanceWidget
from ui.repairs_widget import OverhaulWidget
# from ui.medium_reset_widget import MediumResetWidget  # Disabled for now
from ui.discard_criteria_widget import DiscardCriteriaWidget
from ui.tyre_maintenance_widget import ConditioningWidget
from ui.demand_forecast_widget import DemandForecastWidget

# Configure logger
logger = logging.getLogger('main')

class MainWindow(QMainWindow):
    """Main window for the equipment inventory application."""
    
    def __init__(self):
        """Initialize the main window."""
        super().__init__()
        
        logger.info("Initializing main window")
        self.setWindowTitle(config.APP_NAME)
        
        # Set window icon if available
        if config.APP_ICON:
            try:
                self.setWindowIcon(QIcon(resource_path(config.APP_ICON)))
            except Exception as e:
                logger.warning(f"Could not load app icon: {e}")
        
        # Setup responsive window sizing with low-resolution optimization
        from ui.window_utils import WindowManager, LowResolutionManager

        # Use optimized sizing for low-resolution screens
        if LowResolutionManager.is_low_resolution():
            # For low-res screens, use more aggressive sizing
            WindowManager.setup_responsive_window(self,
                                                width_percent=0.95,
                                                height_percent=0.9,
                                                min_width_percent=0.9,
                                                min_height_percent=0.8)
        else:
            # For higher-res screens, use standard sizing
            WindowManager.setup_responsive_window(self)

        # Enable window state saving/restoring
        self.setObjectName("MainWindow")

        # Try to restore previous window state
        WindowManager.restore_window_state(self, "MainWindow")
        
        # Apply application style
        self.setStyleSheet(config.UI_STYLE)
        
        # Database connection pool is already initialized in main()
        # No need to initialize again here
        
        # Create UI
        self.setup_ui()
        
        # Show status message
        status_bar = self.statusBar()
        if status_bar:
            status_bar.showMessage("Application loaded successfully")
    
    def setup_ui(self):
        """Set up the main window UI."""
        logger.info("Setting up main window UI")
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Create main header
        header_widget = QWidget()
        header_widget.setFixedHeight(80)
        header_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                          stop:0 #2c3e50, stop:1 #3498db);
                border-radius: 8px;
                margin-bottom: 5px;
            }
        """)
        
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Main app title
        app_title = QLabel("PADINAREY MAINTENANCE SOFTWARE(PMS)")
        app_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 28px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
                background: transparent;
                border: none;
            }
        """)
        app_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(app_title)
        
        layout.addWidget(header_widget)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.dashboard_widget = DashboardWidget(self)
        self.equipment_widget = EquipmentWidget(self)
        self.fluids_widget = FluidsWidget(self)
        self.maintenance_widget = MaintenanceWidget(self)
        self.overhaul_widget = OverhaulWidget(self)
        # self.medium_reset_widget = MediumResetWidget(self)  # Disabled for now
        self.discard_criteria_widget = DiscardCriteriaWidget(self)
        self.tyre_maintenance_widget = ConditioningWidget(self)
        self.demand_forecast_widget = DemandForecastWidget(self)
        
        logger.info("Adding tabs to main window")
        self.tab_widget.addTab(self.dashboard_widget, "DASHBOARD")
        self.tab_widget.addTab(self.equipment_widget, "EQUIPMENT")
        self.tab_widget.addTab(self.fluids_widget, "FLUIDS")
        self.tab_widget.addTab(self.maintenance_widget, "MAINTENANCE")
        self.tab_widget.addTab(self.overhaul_widget, "OVERHAUL")
        # self.tab_widget.addTab(self.medium_reset_widget, "Medium Reset")  # Disabled for now
        self.tab_widget.addTab(self.discard_criteria_widget, "DISCARD")
        self.tab_widget.addTab(self.tyre_maintenance_widget, "CONDITIONING")
        self.tab_widget.addTab(self.demand_forecast_widget, "DEMAND")
        
        # Connect tab changed signal
        self.tab_widget.currentChanged.connect(self.tab_changed)

        # Add File menu
        menu_bar = self.menuBar()
        if not menu_bar:
            return
            
        file_menu = menu_bar.addMenu("File")
        if not file_menu:
            return
        
        # Import submenu
        import_menu = file_menu.addMenu("Import")
        if import_menu:
            # Import Excel action
            import_excel_action = QAction("Import Excel Data...", self)
            import_excel_action.triggered.connect(self.import_excel)
            import_menu.addAction(import_excel_action)
            
            import_menu.addSeparator()
            
            # Import Database action
            import_db_action = QAction("Import Database...", self)
            import_db_action.triggered.connect(self.import_database)
            import_menu.addAction(import_db_action)
            
            import_menu.addSeparator()
            
            # Import Policy Docs Path action
            import_policy_path_action = QAction("Import Policy Docs Path...", self)
            import_policy_path_action.triggered.connect(self.import_policy_docs_path)
            import_menu.addAction(import_policy_path_action)
        
        # Export submenu
        export_menu = file_menu.addMenu("Export")
        if export_menu:
            # Export Database action
            export_db_action = QAction("Export Database...", self)
            export_db_action.triggered.connect(self.export_database)
            export_menu.addAction(export_db_action)
            
            export_menu.addSeparator()
            
            # Export Fluid Demand
            export_fluid_action = QAction("Export Fluid Demand...", self)
            export_fluid_action.triggered.connect(lambda: self.export_demand("fluid"))
            export_menu.addAction(export_fluid_action)
            
            # Export Tyre Demand
            export_tyre_action = QAction("Export Tyre Demand...", self)
            export_tyre_action.triggered.connect(lambda: self.export_demand("tyre"))
            export_menu.addAction(export_tyre_action)
            
            # Export Battery Demand
            export_battery_action = QAction("Export Battery Demand...", self)
            export_battery_action.triggered.connect(lambda: self.export_demand("battery"))
            export_menu.addAction(export_battery_action)
        
        file_menu.addSeparator()
        
        # Refresh action
        refresh_action = QAction("Refresh", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.load_initial_data)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        exit_action = QAction("Exit", self)
        app_instance = QApplication.instance()
        if app_instance:
            exit_action.triggered.connect(app_instance.quit)
        file_menu.addAction(exit_action)

        # Create status bar
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # Add version label to status bar
        version_label = QLabel(f"Version: {config.APP_VERSION}")
        status_bar.addPermanentWidget(version_label)
        
        # Load data for first tab
        logger.info("Loading initial data for all widgets")
        self.load_initial_data()
        logger.info("Main window UI setup complete")
    
    def closeEvent(self, event):
        """Handle window close event - save window state."""
        try:
            from ui.window_utils import WindowManager
            WindowManager.save_window_state(self, "MainWindow")
            logger.info("Window state saved")
        except Exception as e:
            logger.warning(f"Could not save window state: {e}")
        
        # Accept the close event
        event.accept()
    
    def resizeEvent(self, event):
        """Handle window resize events."""
        super().resizeEvent(event)
        
        # Update any child widgets that need to know about resize
        current_widget = self.tab_widget.currentWidget()
        if current_widget and hasattr(current_widget, 'on_window_resize'):
            try:
                current_widget.on_window_resize(event.size())
            except Exception as e:
                logger.debug(f"Error handling resize in {current_widget.__class__.__name__}: {e}")
    
    def load_initial_data(self):
        """Load initial data for all tabs."""
        logger.info("Starting initial data load for all widgets")
        try:
            self.dashboard_widget.load_data()
            logger.info("Dashboard data loaded")
            self.equipment_widget.load_data()
            logger.info("Equipment data loaded")
            self.fluids_widget.load_data()
            logger.info("Fluids data loaded")
            self.maintenance_widget.load_data()
            logger.info("Maintenance data loaded")
            self.overhaul_widget.load_data()
            logger.info("Repairs data loaded")

            self.discard_criteria_widget.load_data()
            logger.info("Discard Criteria data loaded")
            self.tyre_maintenance_widget.load_data()
            logger.info("Tyre Maintenance data loaded")            
            self.demand_forecast_widget.load_data()
            logger.info("Demand Forecast data loaded")
            logger.info("Initial data load completed for all widgets")
        except Exception as e:
            logger.error(f"Error loading initial data: {e}")
    
    def tab_changed(self, index):
        """Handle tab change event."""
        # Get current widget
        current_widget = self.tab_widget.widget(index)
        
        # Refresh data in the current tab
        if current_widget and hasattr(current_widget, 'load_data'):
            logger.info(f"Loading data for {current_widget.__class__.__name__}")
            current_widget.load_data()
            logger.info(f"Data loaded for {current_widget.__class__.__name__}")
    
    def switch_to_maintenance_tab(self, equipment_id=None, maintenance_category='TM-1', period_months=6):
        """Switch to maintenance tab and optionally create a new maintenance record."""
        try:
            # Find the maintenance tab index
            maintenance_tab_index = None
            for i in range(self.tab_widget.count()):
                if self.tab_widget.widget(i) == self.maintenance_widget:
                    maintenance_tab_index = i
                    break
            
            if maintenance_tab_index is None:
                logger.error("Maintenance tab not found")
                QMessageBox.critical(
                    self,
                    "Error",
                    "Maintenance tab not found.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Switch to maintenance tab
            self.tab_widget.setCurrentIndex(maintenance_tab_index)
            
            # If equipment_id is provided, create a new maintenance record
            if equipment_id:
                from datetime import datetime, timedelta
                from models import Maintenance
                import config
                import utils
                
                # Get equipment data
                equipment = Equipment.get_by_id(equipment_id)
                if not equipment:
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Equipment not found.",
                        QMessageBox.StandardButton.Ok
                    )
                    return
                
                # Get maintenance types for the category
                maintenance_types = config.MAINTENANCE_TYPES.get(maintenance_category, [])
                if not maintenance_types:
                    maintenance_type = f"{maintenance_category} Maintenance"
                else:
                    maintenance_type = maintenance_types[0]  # Use first type as default
                
                # Create maintenance record with correct parameters
                current_meterage = equipment.get('meterage_kms', 0) if isinstance(equipment, dict) else getattr(equipment, 'meterage_kms', 0)
                current_vintage = equipment.get('vintage_years', 0) if isinstance(equipment, dict) else getattr(equipment, 'vintage_years', 0)
                
                # Calculate due date based on period_months from dialog using proper month calculation
                from dateutil.relativedelta import relativedelta
                today = datetime.now().date()
                due_date = today + relativedelta(months=period_months)
                
                # Create maintenance record - NO done_date, only due_date
                maintenance = Maintenance(
                    equipment_id=equipment_id,
                    maintenance_type=maintenance_type,
                    maintenance_category=maintenance_category,
                    done_date=None,  # No done date - will be set when actually completed
                    next_due_date=due_date.isoformat(),
                    meterage_kms=current_meterage,
                    vintage_years=current_vintage,
                    completion_notes=f'Scheduled from Equipment tab for {maintenance_category}',
                    status='scheduled'  # Mark as scheduled, not completed
                )
                maintenance_id = maintenance.save()
                
                if maintenance_id:
                    # Switch to the appropriate category tab
                    if hasattr(self.maintenance_widget, 'switch_to_category'):
                        self.maintenance_widget.switch_to_category(maintenance_category)
                    
                    # Refresh maintenance data
                    self.maintenance_widget.load_data()
                    
                    # Show success message
                    equipment_name = equipment.get('make_and_type', 'Unknown') if isinstance(equipment, dict) else getattr(equipment, 'make_and_type', 'Unknown')
                    QMessageBox.information(
                        self,
                        "Success",
                        f"Maintenance scheduled successfully for {maintenance_category}.\n"
                        f"Equipment: {equipment_name}\n"
                        f"Next Due Date: {due_date.strftime('%Y-%m-%d')}\n"
                        f"Status: Scheduled (will be marked completed when done)",
                        QMessageBox.StandardButton.Ok
                    )
                    logger.info(f"Created scheduled maintenance record ID {maintenance_id} for equipment {equipment_id}, category {maintenance_category}")
                else:
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to create maintenance record.",
                        QMessageBox.StandardButton.Ok
                    )
            
        except Exception as e:
            logger.error(f"Error switching to maintenance tab: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to switch to maintenance tab:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )

    def view_equipment_maintenance(self, equipment_id):
        """Navigate to maintenance tab and show existing maintenance records for the selected equipment."""
        try:
            # Find the maintenance tab index
            maintenance_tab_index = None
            for i in range(self.tab_widget.count()):
                if self.tab_widget.widget(i) == self.maintenance_widget:
                    maintenance_tab_index = i
                    break
            
            if maintenance_tab_index is None:
                logger.error("Maintenance tab not found")
                QMessageBox.critical(
                    self,
                    "Error",
                    "Maintenance tab not found.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Switch to maintenance tab
            self.tab_widget.setCurrentIndex(maintenance_tab_index)
            
            if equipment_id:
                # Get equipment data
                equipment = Equipment.get_by_id(equipment_id)
                if not equipment:
                    QMessageBox.warning(
                        self,
                        "Equipment Not Found",
                        "The selected equipment could not be found.",
                        QMessageBox.StandardButton.Ok
                    )
                    return
                
                # Get equipment BA number for filtering
                ba_number = equipment.get('ba_number') if isinstance(equipment, dict) else getattr(equipment, 'ba_number', None)
                
                if ba_number:
                    # Filter maintenance records by BA number
                    self.filter_maintenance_by_ba_number(ba_number)
                    
                    # Show success message with delay to allow highlighting to complete
                    equipment_name = equipment.get('make_and_type', 'Unknown') if isinstance(equipment, dict) else getattr(equipment, 'make_and_type', 'Unknown')
                    from PyQt5.QtCore import QTimer
                    
                    def show_success_message():
                        QMessageBox.information(
                            self,
                            "Navigation Complete",
                            f"Showing maintenance records for:\n{equipment_name} (BA: {ba_number})\n\nFiltered records are highlighted in blue.",
                            QMessageBox.StandardButton.Ok
                        )
                    
                    QTimer.singleShot(500, show_success_message)
                    logger.info(f"Navigated to maintenance tab and filtered by BA number: {ba_number}")
                else:
                    QMessageBox.warning(
                        self,
                        "No BA Number",
                        "The selected equipment does not have a BA number assigned.",
                        QMessageBox.StandardButton.Ok
                    )
            
        except Exception as e:
            logger.error(f"Error viewing equipment maintenance: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to view equipment maintenance:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def view_specific_maintenance_record(self, maintenance_id):
        """Navigate to maintenance tab and highlight a specific maintenance record."""
        try:
            # Find the maintenance tab index
            maintenance_tab_index = None
            for i in range(self.tab_widget.count()):
                if self.tab_widget.widget(i) == self.maintenance_widget:
                    maintenance_tab_index = i
                    break
            
            if maintenance_tab_index is None:
                logger.error("Maintenance tab not found")
                QMessageBox.critical(
                    self,
                    "Error",
                    "Maintenance tab not found.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Switch to maintenance tab
            self.tab_widget.setCurrentIndex(maintenance_tab_index)
            
            if maintenance_id:
                # Get maintenance record to determine category and equipment
                import database
                try:
                    with database.get_db_connection() as conn:
                        maintenance = conn.execute(
                            'SELECT * FROM maintenance WHERE maintenance_id = ?',
                            (maintenance_id,)
                        ).fetchone()
                    
                    if maintenance:
                        # Switch to appropriate maintenance category
                        category = maintenance.get('maintenance_category', 'TM-1')
                        if hasattr(self.maintenance_widget, 'switch_to_category'):
                            self.maintenance_widget.switch_to_category(category)
                        
                        # Highlight the specific maintenance record
                        from PyQt5.QtCore import QTimer
                        
                        def highlight_specific_record():
                            self.highlight_specific_maintenance_record(maintenance_id, category)
                        
                        QTimer.singleShot(300, highlight_specific_record)
                        
                        # Show success message with delay
                        def show_success_message():
                            QMessageBox.information(
                                self,
                                "Navigation Complete",
                                f"Found and highlighted maintenance record:\n{maintenance.get('maintenance_type', 'Unknown')} ({category})\n\nRecord is highlighted in blue.",
                                QMessageBox.StandardButton.Ok
                            )
                        
                        QTimer.singleShot(800, show_success_message)
                        
                        logger.info(f"Navigated to specific maintenance record: {maintenance_id}")
                    else:
                        QMessageBox.warning(
                            self,
                            "Record Not Found",
                            "The selected maintenance record could not be found.",
                            QMessageBox.StandardButton.Ok
                        )
                except Exception as db_error:
                    logger.error(f"Database error getting maintenance {maintenance_id}: {db_error}")
                    QMessageBox.critical(
                        self,
                        "Database Error",
                        f"Failed to retrieve maintenance record:\n{str(db_error)}",
                        QMessageBox.StandardButton.Ok
                    )
            
        except Exception as e:
            logger.error(f"Error viewing specific maintenance record: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to view specific maintenance record:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def filter_maintenance_by_ba_number(self, ba_number):
        """Filter maintenance records by BA number across all maintenance categories."""
        try:
            # Get the current maintenance sub-widget
            if hasattr(self.maintenance_widget, 'tab_widget') and self.maintenance_widget.tab_widget:
                current_widget = self.maintenance_widget.tab_widget.currentWidget()
                
                if current_widget and hasattr(current_widget, 'maintenance_table'):
                    maintenance_table = current_widget.maintenance_table
                    
                    # Apply BA number filter if the table supports it
                    if hasattr(maintenance_table, 'ba_filter'):
                        # Set the BA filter text
                        if maintenance_table.ba_filter.isEditable():
                            maintenance_table.ba_filter.lineEdit().setText(str(ba_number))
                        else:
                            maintenance_table.ba_filter.setCurrentText(str(ba_number))
                        
                        # Apply the filter
                        if hasattr(maintenance_table, 'apply_filters'):
                            maintenance_table.apply_filters()
                            logger.info(f"Applied BA filter: {ba_number}")
                        
                        # Refresh the data to show filtered results
                        if hasattr(current_widget, 'load_data'):
                            current_widget.load_data()
                        
                        # Highlight filtered records after a short delay to ensure table is populated
                        from PyQt5.QtCore import QTimer
                        
                        def highlight_records():
                            self.highlight_filtered_maintenance_records(maintenance_table, ba_number)
                        QTimer.singleShot(200, highlight_records)
                
                # Also refresh the maintenance widget data
                if hasattr(self.maintenance_widget, 'load_data'):
                    self.maintenance_widget.load_data()
                    
        except Exception as e:
            logger.error(f"Error filtering maintenance by BA number {ba_number}: {e}")
    
    def highlight_filtered_maintenance_records(self, maintenance_table, ba_number):
        """Highlight all maintenance records that match the filtered BA number."""
        try:
            # Get the actual table widget from the paginated table
            if hasattr(maintenance_table, 'table'):
                table_widget = maintenance_table.table
            else:
                table_widget = maintenance_table
            
            # Ensure table has proper selection styling
            self.ensure_table_selection_styling(table_widget)
            
            # Clear any existing selection
            table_widget.clearSelection()
            
            # Find and select all rows that match the BA number
            rows_to_select = []
            for row in range(table_widget.rowCount()):
                # Check if row is visible (not hidden by filters)
                if not table_widget.isRowHidden(row):
                    # Get BA number from row (usually column 1)
                    ba_item = table_widget.item(row, 1)  # BA Number column
                    if ba_item and str(ba_number).lower() in ba_item.text().lower():
                        rows_to_select.append(row)
            
            # Select all matching rows
            if rows_to_select:
                # Set selection mode to allow multiple selections
                from PyQt5.QtWidgets import QAbstractItemView
                table_widget.setSelectionMode(QAbstractItemView.MultiSelection)
                
                # Select all matching rows
                for row in rows_to_select:
                    table_widget.selectRow(row)
                
                # Apply explicit blue highlighting style to overcome any styling conflicts
                table_widget.setStyleSheet("""
                    QTableWidget::item:selected {
                        background-color: #2196F3 !important;
                        color: white !important;
                    }
                    QTableWidget::item:focus {
                        background-color: #2196F3 !important;
                        color: white !important;
                    }
                """)
                
                # Scroll to first selected row
                if rows_to_select:
                    table_widget.scrollToItem(table_widget.item(rows_to_select[0], 1))
                
                # Force visual refresh to ensure highlighting appears
                table_widget.viewport().update()
                
                logger.info(f"Highlighted {len(rows_to_select)} maintenance records for BA number: {ba_number}")
                
                # Restore original selection mode after highlighting
                def restore_selection_mode():
                    table_widget.setSelectionMode(QAbstractItemView.SingleSelection)
                QTimer.singleShot(1000, restore_selection_mode)
            else:
                logger.info(f"No maintenance records found for BA number: {ba_number}")
                
        except Exception as e:
            logger.error(f"Error highlighting maintenance records for BA {ba_number}: {e}")
    
    def ensure_table_selection_styling(self, table_widget):
        """Ensure the table widget has proper blue selection styling."""
        try:
            # Apply explicit blue highlighting style that overrides any existing styles
            current_style = table_widget.styleSheet()
            selection_style = """
                QTableWidget::item:selected {
                    background-color: #2196F3 !important;
                    color: white !important;
                }
                QTableWidget::item:focus {
                    background-color: #2196F3 !important;
                    color: white !important;
                }
            """
            
            # Combine existing style with selection style
            combined_style = current_style + selection_style
            table_widget.setStyleSheet(combined_style)
            
        except Exception as e:
            logger.error(f"Error ensuring table selection styling: {e}")
    
    def highlight_specific_maintenance_record(self, maintenance_id, category):
        """Highlight a specific maintenance record by its ID."""
        try:
            # Get the current maintenance sub-widget for the specific category
            if hasattr(self.maintenance_widget, 'tab_widget') and self.maintenance_widget.tab_widget:
                # Switch to the correct category tab first
                for i in range(self.maintenance_widget.tab_widget.count()):
                    widget = self.maintenance_widget.tab_widget.widget(i)
                    if widget and hasattr(widget, 'category') and widget.category == category:
                        self.maintenance_widget.tab_widget.setCurrentIndex(i)
                        break
                
                # Get the current widget
                current_widget = self.maintenance_widget.tab_widget.currentWidget()
                
                if current_widget and hasattr(current_widget, 'maintenance_table'):
                    maintenance_table = current_widget.maintenance_table
                    
                    # Get the actual table widget from the paginated table
                    if hasattr(maintenance_table, 'table'):
                        table_widget = maintenance_table.table
                    else:
                        table_widget = maintenance_table
                    
                    # Ensure table has proper selection styling
                    self.ensure_table_selection_styling(table_widget)
                    
                    # Clear any existing selection
                    table_widget.clearSelection()
                    
                    # Find and select the specific maintenance record
                    for row in range(table_widget.rowCount()):
                        # Check if row is visible (not hidden by filters)
                        if not table_widget.isRowHidden(row):
                            # Get ID from row (usually column 0, but it's hidden)
                            # Check if this is our maintenance record by looking at row data
                            if hasattr(maintenance_table, 'filtered_data'):
                                # For paginated table, get from filtered data
                                try:
                                    page_offset = getattr(maintenance_table, 'current_page', 0) * getattr(maintenance_table, 'page_size', 100)
                                    data_index = page_offset + row
                                    if data_index < len(maintenance_table.filtered_data):
                                        row_data = maintenance_table.filtered_data[data_index]
                                        if row_data.get('ID') == maintenance_id:
                                            # Select and highlight this row
                                            table_widget.selectRow(row)
                                            
                                            # Apply explicit blue highlighting style
                                            table_widget.setStyleSheet("""
                                                QTableWidget::item:selected {
                                                    background-color: #2196F3 !important;
                                                    color: white !important;
                                                }
                                                QTableWidget::item:focus {
                                                    background-color: #2196F3 !important;
                                                    color: white !important;
                                                }
                                            """)
                                            
                                            table_widget.scrollToItem(table_widget.item(row, 1))  # Scroll to visible column
                                            
                                            # Force visual refresh to ensure highlighting appears
                                            table_widget.viewport().update()
                                            
                                            logger.info(f"Found and highlighted specific maintenance record: {maintenance_id}")
                                            return
                                except Exception as e:
                                    logger.debug(f"Error checking filtered data: {e}")
                            
                            # Fallback: check UserRole data in cells
                            for col in range(table_widget.columnCount()):
                                item = table_widget.item(row, col)
                                if item:
                                    from PyQt5.QtCore import Qt
                                    user_data = item.data(Qt.ItemDataRole.UserRole)
                                    if user_data == maintenance_id:
                                        table_widget.selectRow(row)
                                        
                                        # Apply explicit blue highlighting style
                                        table_widget.setStyleSheet("""
                                            QTableWidget::item:selected {
                                                background-color: #2196F3 !important;
                                                color: white !important;
                                            }
                                            QTableWidget::item:focus {
                                                background-color: #2196F3 !important;
                                                color: white !important;
                                            }
                                        """)
                                        
                                        table_widget.scrollToItem(item)
                                        
                                        # Force visual refresh to ensure highlighting appears
                                        table_widget.viewport().update()
                                        
                                        logger.info(f"Found and highlighted specific maintenance record (UserRole): {maintenance_id}")
                                        return
                    
                    logger.warning(f"Specific maintenance record {maintenance_id} not found in current view")
                    
        except Exception as e:
            logger.error(f"Error highlighting specific maintenance record {maintenance_id}: {e}")
 
    def import_excel(self):
        """Invoke Excel import with preview and conflict resolution."""
        fname, _ = QFileDialog.getOpenFileName(
            self, "Select Excel File", "", "Excel Files (*.xls *.xlsx)"
        )
        if fname:
            # Show import preview dialog first
            self.show_import_preview(fname)

    def show_import_preview(self, filename):
        """Show import preview dialog with conflict resolution."""
        try:
            from ui.excel_import_preview_dialog import ExcelDataAnalyzer, ImportPreviewDialog
            from PyQt5.QtWidgets import QProgressDialog, QApplication
            from PyQt5.QtCore import Qt

            # Show analysis progress
            progress = QProgressDialog("Analyzing Excel file...", "Cancel", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setMinimumDuration(500)
            progress.setValue(10)
            QApplication.processEvents()

            # Analyze Excel file
            analyzer = ExcelDataAnalyzer()
            progress.setValue(50)
            QApplication.processEvents()

            analysis_data = analyzer.analyze_excel_file(filename)
            progress.setValue(90)
            QApplication.processEvents()

            progress.close()

            if not analysis_data.get('success', False):
                error_msg = analysis_data.get('error', 'Unknown error during file analysis')
                QMessageBox.critical(
                    self,
                    "Analysis Error",
                    f"Failed to analyze Excel file:\n{error_msg}",
                    QMessageBox.StandardButton.Ok
                )
                return

            # Check if there are conflicts
            conflicts = analysis_data.get('conflicts', [])

            if conflicts:
                # Show full preview dialog with conflict resolution
                preview_dialog = ImportPreviewDialog(analysis_data, self)
                preview_dialog.import_confirmed.connect(self.proceed_with_import)
                preview_dialog.import_cancelled.connect(self.import_cancelled)
                preview_dialog.exec_()
            else:
                # Show simple confirmation dialog for non-conflicting imports
                from ui.excel_import_preview_dialog import SimpleConfirmationDialog
                from PyQt5.QtWidgets import QDialog

                confirmation_dialog = SimpleConfirmationDialog(analysis_data, self)
                if confirmation_dialog.exec_() == QDialog.Accepted:
                    self.proceed_with_import(analysis_data)
                else:
                    self.import_cancelled()

        except Exception as e:
            logger.error(f"Error in import preview: {e}")
            QMessageBox.critical(
                self,
                "Preview Error",
                f"Failed to show import preview:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )

    def proceed_with_import(self, analysis_data):
        """Proceed with actual import after preview confirmation."""
        filename = analysis_data.get('file_path')
        conflict_resolutions = analysis_data.get('conflict_resolutions', {})

        logger.info(f"Proceeding with import: {filename}")
        logger.info(f"Conflict resolutions: {len(conflict_resolutions)} conflicts resolved")

        # Show loading screen for actual import
        self.show_import_loading_screen(filename, conflict_resolutions)

    def import_cancelled(self):
        """Handle import cancellation."""
        logger.info("Excel import cancelled by user")
        QMessageBox.information(
            self,
            "Import Cancelled",
            "Excel import has been cancelled.",
            QMessageBox.StandardButton.Ok
        )

    def show_import_loading_screen(self, filename, conflict_resolutions=None):
        """Show a beautiful loading screen during Excel import."""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                                   QProgressBar, QFrame, QPushButton, QApplication)
        from PyQt5.QtCore import QThread, QTimer, pyqtSignal, QEventLoop
        from PyQt5.QtGui import QFont, QMovie, QPixmap
        import os
        
        class ImportWorker(QThread):
            """Worker thread for Excel import to prevent UI freezing."""
            progress_update = pyqtSignal(int, str)  # progress, message
            import_complete = pyqtSignal(dict)  # stats
            import_error = pyqtSignal(str)  # error message

            def __init__(self, filename, conflict_resolutions=None):
                super().__init__()
                self.filename = filename
                self.conflict_resolutions = conflict_resolutions or {}
            
            def run(self):
                """Perform the Excel import with progress updates."""
                try:
                    self.progress_update.emit(10, "Opening Excel file...")
                    QThread.msleep(200)  # Small delay for UI update
                    
                    # Import pandas here to show loading progress
                    import pandas as pd
                    # Use unified Excel importer
                    from unified_excel_importer import import_from_excel
                    
                    self.progress_update.emit(20, "Reading Excel sheets...")
                    QThread.msleep(200)
                    
                    # Pre-analyze file to estimate progress
                    try:
                        xls = pd.ExcelFile(self.filename)
                        total_sheets = len(xls.sheet_names)
                        self.progress_update.emit(30, f"Found {total_sheets} sheet(s) to process...")
                        QThread.msleep(300)
                    except Exception:
                        total_sheets = 1
                    
                    self.progress_update.emit(40, "Processing equipment data...")
                    QThread.msleep(200)
                    
                    # Perform the actual import with conflict resolutions
                    if self.conflict_resolutions:
                        # Import with conflict resolution handling
                        stats = self._import_with_conflict_resolution()
                    else:
                        # Standard import
                        stats = import_from_excel(self.filename)
                    
                    self.progress_update.emit(90, "Finalizing import...")
                    QThread.msleep(300)
                    
                    self.progress_update.emit(100, "Import completed successfully!")
                    QThread.msleep(500)
                    
                    self.import_complete.emit(stats)
                    
                except Exception as e:
                    import traceback
                    logger.error(f"Excel import error: {e}")
                    logger.error(f"Excel import traceback: {traceback.format_exc()}")
                    self.import_error.emit(str(e))

            def _import_with_conflict_resolution(self):
                """Import Excel file with conflict resolution handling."""
                try:
                    from robust_excel_importer_working import RobustExcelImporter

                    # Create importer instance
                    importer = RobustExcelImporter()

                    # Initialize staging database
                    if not importer.initialize_staging():
                        raise Exception("Failed to initialize staging database")

                    # Process the Excel file (conflict resolution handled separately)
                    success, stats = importer.process_excel_file(self.filename)

                    if not success:
                        raise Exception(f"Import with conflict resolution failed: {stats}")

                    return stats

                except Exception as e:
                    # Fallback to unified Excel importer
                    logger.warning(f"Conflict resolution import failed, falling back to unified importer: {e}")
                    from unified_excel_importer import import_from_excel
                    return import_from_excel(self.filename)
        
        class LoadingDialog(QDialog):
            """Beautiful loading dialog for Excel import."""
            
            def __init__(self, filename, parent=None, conflict_resolutions=None):
                super().__init__(parent)
                self.filename = filename
                self.conflict_resolutions = conflict_resolutions or {}
                self.worker = None
                self.is_completed = False
                self.setup_ui()
                
                # Start import after dialog is shown
                QTimer.singleShot(100, self.start_import)
            
            def setup_ui(self):
                """Set up the loading dialog UI."""
                self.setWindowTitle("Importing Excel Data")
                self.setFixedSize(500, 350)
                self.setModal(True)
                
                # Remove window buttons except close
                from PyQt5.QtCore import Qt
                # Type ignore for enum combination - works at runtime  
                self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.CustomizeWindowHint | Qt.WindowType.WindowTitleHint)  # type: ignore
                
                # Main layout with margins
                main_layout = QVBoxLayout(self)
                main_layout.setSpacing(25)
                main_layout.setContentsMargins(30, 30, 30, 30)
                
                # Header section with fixed height
                header_frame = QFrame()
                header_frame.setFixedHeight(100)
                header_frame.setObjectName("headerFrame")
                header_layout = QVBoxLayout(header_frame)
                header_layout.setContentsMargins(20, 15, 20, 15)
                
                # Title with fixed font
                title_label = QLabel("📊 Importing Excel Data")
                title_font = QFont("Segoe UI", 18, QFont.Bold)
                title_label.setFont(title_font)
                title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                title_label.setStyleSheet("""
                    QLabel {
                        color: #2c3e50;
                        background: transparent;
                        padding: 5px;
                    }
                """)
                header_layout.addWidget(title_label)
                
                # File info with truncated name if too long
                filename_only = os.path.basename(self.filename)
                if len(filename_only) > 40:
                    filename_only = filename_only[:37] + "..."
                
                file_label = QLabel(f"File: {filename_only}")
                file_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                file_label.setStyleSheet("""
                    QLabel {
                        color: #7f8c8d;
                        font-size: 12px;
                        background: transparent;
                        padding: 2px;
                    }
                """)
                header_layout.addWidget(file_label)
                
                main_layout.addWidget(header_frame)
                
                # Progress section with fixed height
                progress_frame = QFrame()
                progress_frame.setFixedHeight(150)
                progress_frame.setObjectName("progressFrame")
                progress_layout = QVBoxLayout(progress_frame)
                progress_layout.setContentsMargins(25, 20, 25, 20)
                progress_layout.setSpacing(15)
                
                # Loading icon
                self.loading_label = QLabel("⟳")
                self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.loading_label.setFixedHeight(40)
                self.loading_label.setStyleSheet("""
                    QLabel {
                        font-size: 28px;
                        color: #3498db;
                        background: transparent;
                        border: none;
                    }
                """)
                progress_layout.addWidget(self.loading_label)
                
                # Progress bar with fixed height
                self.progress_bar = QProgressBar()
                self.progress_bar.setRange(0, 100)
                self.progress_bar.setValue(0)
                self.progress_bar.setFixedHeight(30)
                self.progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: 2px solid #bdc3c7;
                        border-radius: 10px;
                        text-align: center;
                        font-weight: bold;
                        font-size: 11px;
                        background-color: #ecf0f1;
                    }
                    QProgressBar::chunk {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                                  stop:0 #3498db, stop:1 #2980b9);
                        border-radius: 8px;
                        margin: 1px;
                    }
                """)
                progress_layout.addWidget(self.progress_bar)
                
                # Status message with fixed height
                self.status_label = QLabel("Preparing to import...")
                self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.status_label.setFixedHeight(30)
                self.status_label.setWordWrap(True)
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #2c3e50;
                        font-size: 12px;
                        background: transparent;
                        padding: 5px;
                    }
                """)
                progress_layout.addWidget(self.status_label)
                
                main_layout.addWidget(progress_frame)
                
                # Button section with fixed height
                button_frame = QFrame()
                button_frame.setFixedHeight(50)
                button_layout = QHBoxLayout(button_frame)
                button_layout.addStretch()
                
                self.close_button = QPushButton("Close")
                self.close_button.setFixedSize(80, 35)
                self.close_button.clicked.connect(self.accept)
                self.close_button.setVisible(False)
                self.close_button.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 11px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                    QPushButton:pressed {
                        background-color: #21618c;
                    }
                """)
                button_layout.addWidget(self.close_button)
                button_layout.addStretch()
                
                main_layout.addWidget(button_frame)
                
                # Start rotation animation
                self.rotation_timer = QTimer()
                self.rotation_timer.timeout.connect(self.rotate_loading_icon)
                self.rotation_timer.start(150)  # Slower rotation
                self.rotation_angle = 0
                
                # Apply dialog styles
                self.setStyleSheet("""
                    QDialog {
                        background-color: #f8f9fa;
                        border: 2px solid #dee2e6;
                        border-radius: 12px;
                    }
                    QFrame#headerFrame {
                        background-color: white;
                        border: 1px solid #e9ecef;
                        border-radius: 8px;
                    }
                    QFrame#progressFrame {
                        background-color: white;
                        border: 1px solid #e9ecef;
                        border-radius: 8px;
                    }
                """)
            
            def rotate_loading_icon(self):
                """Animate the loading icon rotation."""
                if not self.is_completed:
                    icons = ["⟳", "⟲"]
                    self.rotation_angle = (self.rotation_angle + 1) % len(icons)
                    self.loading_label.setText(icons[self.rotation_angle])
            
            def start_import(self):
                """Start the import process."""
                self.worker = ImportWorker(self.filename, self.conflict_resolutions)
                self.worker.progress_update.connect(self.update_progress)
                self.worker.import_complete.connect(self.import_finished)
                self.worker.import_error.connect(self.import_failed)
                self.worker.start()
            
            def update_progress(self, value, message):
                """Update progress bar and status message."""
                self.progress_bar.setValue(value)
                self.status_label.setText(message)
                QApplication.processEvents()  # Force UI update
            
            def import_finished(self, stats):
                """Handle successful import completion."""
                self.is_completed = True
                self.rotation_timer.stop()
                
                self.loading_label.setText("✅")
                self.loading_label.setStyleSheet("""
                    QLabel {
                        font-size: 28px;
                        color: #27ae60;
                        background: transparent;
                        border: none;
                    }
                """)
                
                # Calculate total records
                total_records = sum([
                    stats.get('equipment', 0),
                    stats.get('fluids', 0),
                    stats.get('tyres', 0),
                    stats.get('batteries', 0),
                    stats.get('repairs', 0)
                ])
                
                # Show completion message
                completion_msg = "Import completed successfully!\n\n"
                completion_msg += f"📋 Equipment: {stats.get('equipment', 0)}\n"
                completion_msg += f"🛢️ Fluids: {stats.get('fluids', 0)}\n"
                completion_msg += f"🚗 Tyres: {stats.get('tyres', 0)}\n"
                completion_msg += f"🔋 Batteries: {stats.get('batteries', 0)}\n"
                completion_msg += f"🔧 Repairs: {stats.get('repairs', 0)}\n"
                if stats.get('skipped', 0) > 0:
                    completion_msg += f"⚠️ Skipped: {stats.get('skipped', 0)}\n"
                completion_msg += f"\nTotal records: {total_records}"
                
                self.status_label.setText(completion_msg)
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #27ae60;
                        font-size: 10px;
                        background: transparent;
                        padding: 5px;
                    }
                """)
                
                # Show close button
                self.close_button.setVisible(True)
                
                # Auto-close after 4 seconds
                QTimer.singleShot(4000, self.auto_close)
                
                # Reload data in main window
                parent_widget = self.parent()
                if parent_widget and hasattr(parent_widget, 'load_initial_data'):
                    # Type: ignore for linter - we know this method exists on MainWindow
                    QTimer.singleShot(500, parent_widget.load_initial_data)  # type: ignore
            
            def import_failed(self, error_message):
                """Handle import failure."""
                self.is_completed = True
                self.rotation_timer.stop()
                
                self.loading_label.setText("❌")
                self.loading_label.setStyleSheet("""
                    QLabel {
                        font-size: 28px;
                        color: #e74c3c;
                        background: transparent;
                        border: none;
                    }
                """)
                
                self.progress_bar.setValue(0)
                self.status_label.setText(f"Import failed:\n{error_message}")
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #e74c3c;
                        font-size: 11px;
                        background: transparent;
                        padding: 5px;
                    }
                """)
                
                # Show close button
                self.close_button.setVisible(True)
            
            def auto_close(self):
                """Auto-close dialog if still open."""
                if self.isVisible() and self.close_button.isVisible():
                    self.accept()
            
            def closeEvent(self, event):
                """Handle dialog close event."""
                if self.worker and self.worker.isRunning():
                    self.worker.terminate()
                    self.worker.wait(3000)  # Wait up to 3 seconds
                if hasattr(self, 'rotation_timer'):
                    self.rotation_timer.stop()
                event.accept()
        
        # Show the loading dialog
        dialog = LoadingDialog(filename, self, conflict_resolutions)
        dialog.exec_()
    
    def export_database(self):
        """Export the entire database to a shareable file."""
        from PyQt5.QtWidgets import QMessageBox, QFileDialog, QProgressDialog, QApplication
        from datetime import datetime
        import config
        import os
        from database_manager import SimpleDatabaseManager
        
        try:
            # Get save location
            default_filename = f"inventory_database_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Export Database",
                default_filename,
                "Database Files (*.db);;All Files (*)"
            )
            
            if not filename:
                return
            
            if not filename.lower().endswith('.db'):
                filename += '.db'
            
            # Create a simple progress dialog
            progress = QProgressDialog("Exporting database...", "Cancel", 0, 100, self)
            progress.setWindowTitle("Database Export")
            progress.setModal(True)
            progress.setAutoClose(False)
            progress.setAutoReset(False)
            progress.setMinimumWidth(400)
            progress.show()
            
            # Perform export with progress updates
            try:
                progress.setValue(10)
                progress.setLabelText("Preparing database export...")
                QApplication.processEvents()
                
                # CRITICAL FIX: Ensure database connections are properly closed before export
                progress.setLabelText("Ensuring database consistency...")
                QApplication.processEvents()
                
                # Force close all database connections and commit any pending transactions
                try:
                    from database import get_connection_pool
                    get_connection_pool().close_all()
                    logger.info("Closed all database connections before export")
                except Exception as e:
                    logger.warning(f"Could not close database connections: {e}")
                
                # Small delay to ensure all connections are closed
                import time
                time.sleep(0.5)
                
                # Get current database path (same as what the application is using)
                current_db_path = config.DB_PATH
                logger.info(f"Exporting database from: {current_db_path}")
                
                # Verify the database we're about to export has the expected data
                try:
                    import sqlite3
                    conn = sqlite3.connect(current_db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM equipment")
                    equipment_count = cursor.fetchone()[0]
                    conn.close()
                    logger.info(f"Pre-export verification: {equipment_count} equipment records found")
                    
                    if equipment_count == 0:
                        progress.close()
                        QMessageBox.warning(self, "Export Warning", 
                                          "The database appears to be empty. Please check your data before exporting.")
                        return
                        
                except Exception as e:
                    logger.error(f"Pre-export verification failed: {e}")
                
                # Create database manager
                manager = SimpleDatabaseManager(current_db_path)
                
                progress.setValue(30)
                progress.setLabelText("Copying database file...")
                QApplication.processEvents()
                
                # Check if user cancelled
                if progress.wasCanceled():
                    progress.close()
                    return
                
                # Export database
                result = manager.export_database(filename)
                
                progress.setValue(90)
                progress.setLabelText("Finalizing export...")
                QApplication.processEvents()
                
                # Check if user cancelled
                if progress.wasCanceled():
                    progress.close()
                    return
                
                progress.setValue(100)
                progress.setLabelText("Export completed!")
                QApplication.processEvents()
                
                progress.close()
                
                if result.get('success'):
                    stats = result.get('stats', {})
                    size_mb = result.get('file_size', 0) / (1024 * 1024)
                    
                    message = f"Database exported successfully! 🎉\n\n"
                    message += f"📁 File: {os.path.basename(result['target_path'])}\n"
                    message += f"💾 Size: {size_mb:.1f} MB\n\n"
                    message += f"📊 Records exported:\n"
                    
                    # Display record counts
                    record_fields = ['equipment', 'fluids', 'maintenance', 'repairs', 'overhauls']
                    for field in record_fields:
                        if field in stats:
                            message += f"   • {field.replace('_', ' ').title()}: {stats[field]}\n"
                    
                    message += f"\n📈 Total Records: {stats.get('total_records', 0)}"
                    
                    QMessageBox.information(self, "Export Complete", message)
                    logger.info(f"Database exported successfully to: {filename}")
                else:
                    error = result.get('error', 'Unknown export error')
                    QMessageBox.critical(self, "Export Failed", f"Failed to export database:\n\n{error}")
                    logger.error(f"Database export failed: {error}")
                
            except Exception as e:
                progress.close()
                error_msg = f"Export error: {str(e)}"
                logger.error(f"Database export error: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                QMessageBox.critical(self, "Export Error", error_msg)
                
        except Exception as e:
            error_msg = f"Failed to initialize database export: {str(e)}"
            logger.error(f"Error in export_database: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            QMessageBox.critical(self, "Export Error", error_msg)

    def import_database(self):
        """Import database from a backup file."""
        from PyQt5.QtWidgets import (QMessageBox, QFileDialog, QProgressDialog, QApplication,
                                   QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                                   QPushButton, QRadioButton, QButtonGroup)
        from datetime import datetime
        import config
        import os
        from database_manager import SimpleDatabaseManager
        
        try:
            # Select backup file
            filename, _ = QFileDialog.getOpenFileName(
                self,
                "Import Database",
                "",
                "Database Files (*.db);;All Files (*)"
            )
            
            if not filename:
                return
            
            # Verify backup file
            try:
                manager = SimpleDatabaseManager(config.DB_PATH)
                source_stats = manager.get_database_stats(filename)
                
                if 'error' in source_stats:
                    QMessageBox.critical(self, "Invalid Database", 
                                       f"This does not appear to be a valid database file.\n"
                                       f"Error: {source_stats['error']}")
                    return
            
            except Exception as e:
                QMessageBox.critical(self, "Invalid Database", 
                                   f"Could not read database file:\n{str(e)}")
                return
            
            # Show import options dialog
            class ImportOptionsDialog(QDialog):
                def __init__(self, source_stats, parent=None):
                    super().__init__(parent)
                    self.source_stats = source_stats
                    self.setup_ui()
                
                def setup_ui(self):
                    self.setWindowTitle("Database Import Options")
                    self.setFixedSize(500, 400)
                    self.setModal(True)
                    
                    layout = QVBoxLayout(self)
                    
                    # Header
                    header = QLabel("Database Import Options")
                    header.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
                    layout.addWidget(header)
                    
                    # Database info
                    info_text = f"📋 Database Information:\n"
                    info_text += f"   • Total Records: {self.source_stats.get('total_records', 0)}\n"
                    info_text += f"   • Equipment: {self.source_stats.get('equipment', 0)}\n"
                    info_text += f"   • Maintenance: {self.source_stats.get('maintenance', 0)}\n"
                    info_text += f"   • Fluids: {self.source_stats.get('fluids', 0)}\n"
                    info_text += f"   • Size: {self.source_stats.get('database_size_mb', 0)} MB"
                    
                    info_label = QLabel(info_text)
                    info_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
                    info_label.setWordWrap(True)
                    layout.addWidget(info_label)
                    
                    # Warning
                    warning = QLabel("⚠ Warning: This will replace ALL existing data in your database!")
                    warning.setStyleSheet("color: red; font-weight: bold; margin: 10px 0;")
                    layout.addWidget(warning)
                    
                    # Import options
                    options_label = QLabel("Import Options:")
                    options_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
                    layout.addWidget(options_label)
                    
                    self.button_group = QButtonGroup()
                    
                    # Option 1: Replace all
                    self.replace_radio = QRadioButton("Replace all data (Recommended)")
                    self.replace_radio.setChecked(True)
                    self.button_group.addButton(self.replace_radio)
                    layout.addWidget(self.replace_radio)
                    
                    replace_desc = QLabel("   • Creates backup of current database\n"
                                         "   • Replaces with imported database\n"
                                         "   • Guarantees exact replica of source system")
                    replace_desc.setStyleSheet("color: #666; margin-left: 20px; margin-bottom: 10px;")
                    layout.addWidget(replace_desc)
                    
                    # Option 2: Merge (now available!)
                    self.merge_radio = QRadioButton("Merge with existing data")
                    self.merge_radio.setEnabled(True)
                    self.button_group.addButton(self.merge_radio)
                    layout.addWidget(self.merge_radio)
                    
                    merge_desc = QLabel("   • Combines imported data with existing data\n"
                                       "   • Handles ID conflicts intelligently\n"
                                       "   • Provides conflict resolution options")
                    merge_desc.setStyleSheet("color: #666; margin-left: 20px; margin-bottom: 10px;")
                    layout.addWidget(merge_desc)
                    
                    # Buttons
                    button_layout = QHBoxLayout()
                    button_layout.addStretch()
                    
                    self.cancel_button = QPushButton("Cancel")
                    self.cancel_button.clicked.connect(self.reject)
                    button_layout.addWidget(self.cancel_button)
                    
                    self.import_button = QPushButton("Import Database")
                    self.import_button.setStyleSheet("""
                        QPushButton {
                            background-color: #d32f2f;
                            color: white;
                            font-weight: bold;
                            padding: 8px 16px;
                            border-radius: 4px;
                        }
                        QPushButton:hover {
                            background-color: #b71c1c;
                        }
                    """)
                    self.import_button.clicked.connect(self.accept)
                    button_layout.addWidget(self.import_button)
                    
                    layout.addLayout(button_layout)
                
                def get_import_mode(self):
                    if self.replace_radio.isChecked():
                        return 'replace'
                    elif self.merge_radio.isChecked():
                        return 'merge'
                    return 'replace'
            
            # Show options dialog
            options_dialog = ImportOptionsDialog(source_stats, self)
            if options_dialog.exec_() != QDialog.Accepted:
                return
            
            import_mode = options_dialog.get_import_mode()
            
            # Confirm the operation
            if import_mode == 'replace':
                confirm = QMessageBox.question(
                    self,
                    "Confirm Database Import",
                    "This will PERMANENTLY REPLACE all your current data!\n\n"
                    "A backup of your current database will be created first.\n\n"
                    "Are you absolutely sure you want to continue?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if confirm != QMessageBox.Yes:
                    return
            elif import_mode == 'merge':
                confirm = QMessageBox.question(
                    self,
                    "Confirm Database Merge",
                    "This will MERGE the imported database with your existing data.\n\n"
                    "• New records will be added\n"
                    "• Conflicts will be detected and resolved\n"
                    "• A backup of your current database will be created first\n\n"
                    "Continue with merge operation?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if confirm != QMessageBox.Yes:
                    return
            
            # Create a simple progress dialog
            progress = QProgressDialog("Importing database...", "Cancel", 0, 100, self)
            progress.setWindowTitle("Database Import")
            progress.setModal(True)
            progress.setAutoClose(False)
            progress.setAutoReset(False)
            progress.setMinimumWidth(400)
            progress.show()
            
            # Perform import with progress updates
            try:
                progress.setValue(10)
                progress.setLabelText("Preparing database import...")
                QApplication.processEvents()
                
                # Check if user cancelled
                if progress.wasCanceled():
                    progress.close()
                    return
                
                # Create database manager
                manager = SimpleDatabaseManager(config.DB_PATH)
                
                progress.setValue(30)
                progress.setLabelText("Creating backup of current database...")
                QApplication.processEvents()
                
                # Check if user cancelled
                if progress.wasCanceled():
                    progress.close()
                    return
                
                progress.setValue(50)
                progress.setLabelText("Closing database connections...")
                QApplication.processEvents()
                
                # Check if user cancelled
                if progress.wasCanceled():
                    progress.close()
                    return
                
                # Close all database connections to release file lock
                try:
                    import database
                    pool = database.get_connection_pool()
                    if pool:
                        pool.close_all()
                        logger.info("Database connections closed for import")
                except Exception as e:
                    logger.warning(f"Could not close database connections: {e}")
                
                progress.setValue(60)
                progress.setLabelText("Importing new database...")
                QApplication.processEvents()
                
                # Check if user cancelled
                if progress.wasCanceled():
                    progress.close()
                    return
                
                # Import database (file lock should now be released)
                result = manager.import_database(filename, import_mode)
                
                progress.close()
                
                # Handle conflicts if detected (merge mode only)
                if import_mode == 'merge' and result.get('conflicts_detected'):
                    conflicts = result.get('conflicts', [])
                    conflict_resolutions = self.handle_merge_conflicts(conflicts, manager, filename, source_stats)
                    if conflict_resolutions is not None:
                        # Conflicts resolved, retry import with resolutions
                        result = manager.import_database(filename, import_mode, conflict_resolutions)
                    else:
                        # User cancelled conflict resolution
                        return
                
                if result.get('success'):
                    if import_mode == 'replace':
                        # Replace mode success message
                        stats = result.get('imported_stats', {})
                        backup_path = result.get('backup_path', 'Unknown')
                        
                        message = f"Database replaced successfully! 🎉\n\n"
                        message += f"📁 Source: {os.path.basename(filename)}\n"
                        message += f"💾 Backup created: {os.path.basename(backup_path) if backup_path != 'Unknown' else 'N/A'}\n\n"
                        message += f"📊 Records imported:\n"
                        
                        # Display record counts
                        record_fields = ['equipment', 'fluids', 'maintenance', 'repairs', 'overhauls']
                        for field in record_fields:
                            if field in stats:
                                message += f"   • {field.replace('_', ' ').title()}: {stats[field]}\n"
                        
                        message += f"\n📈 Total Records: {stats.get('total_records', 0)}\n\n"
                        message += "⚠ The application will restart to apply changes."
                        
                    else:  # merge mode
                        # Merge mode success message
                        merge_result = result.get('merge_result', {})
                        final_stats = result.get('final_stats', {})
                        
                        message = f"Database merged successfully! 🎉\n\n"
                        message += f"📁 Source: {os.path.basename(filename)}\n\n"
                        message += f"📊 Merge Summary:\n"
                        
                        if merge_result:
                            # Equipment statistics with detailed breakdown
                            message += f"   📦 Equipment:\n"
                            message += f"      ✅ Added: {merge_result.get('equipment_added', 0)}\n"
                            if merge_result.get('equipment_updated', 0) > 0:
                                message += f"      🔄 Updated: {merge_result.get('equipment_updated', 0)}\n"
                            if merge_result.get('equipment_skipped', 0) > 0:
                                message += f"      ⏭️ Skipped (duplicates): {merge_result.get('equipment_skipped', 0)}\n"
                            
                            # Other record types
                            message += f"   🛢️ Fluids added: {merge_result.get('fluids_added', 0)}\n"
                            message += f"   🔧 Maintenance added: {merge_result.get('maintenance_added', 0)}\n"
                            
                            # Calculate total equipment processed
                            total_equipment_processed = (
                                merge_result.get('equipment_added', 0) + 
                                merge_result.get('equipment_updated', 0) + 
                                merge_result.get('equipment_skipped', 0)
                            )
                            message += f"\n📋 Total equipment processed: {total_equipment_processed}\n"
                        
                        message += f"📈 Total Records After Merge: {final_stats.get('total_records', 0)}\n\n"
                        message += "⚠ The application will restart to apply changes."
                    
                    title = "Import Complete" if import_mode == 'replace' else "Merge Complete"
                    QMessageBox.information(self, title, message)
                    logger.info(f"Database imported successfully from: {filename}")
                    
                    # Restart application to refresh all data
                    self.restart_application()
                else:
                    error = result.get('error', 'Unknown import error')
                    QMessageBox.critical(self, "Import Failed", f"Failed to import database:\n\n{error}")
                    logger.error(f"Database import failed: {error}")
                
            except Exception as e:
                progress.close()
                error_msg = f"Import error: {str(e)}"
                logger.error(f"Database import error: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                QMessageBox.critical(self, "Import Error", error_msg)
                
        except Exception as e:
            error_msg = f"Failed to initialize database import: {str(e)}"
            logger.error(f"Error in import_database: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            QMessageBox.critical(self, "Import Error", error_msg)

    def handle_merge_conflicts(self, conflicts, manager, filename, source_stats):
        """Handle database merge conflicts with user resolution."""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                                   QPushButton, QTableWidget, QTableWidgetItem, 
                                   QHeaderView, QButtonGroup, QRadioButton, QGroupBox)
        
        class ConflictResolutionDialog(QDialog):
            def __init__(self, conflicts, source_stats, parent=None):
                super().__init__(parent)
                self.conflicts = conflicts
                self.source_stats = source_stats
                self.resolutions = {}
                self.setup_ui()
            
            def setup_ui(self):
                self.setWindowTitle("Resolve Database Conflicts")
                self.setModal(True)
                self.setMinimumSize(800, 600)
                
                layout = QVBoxLayout(self)
                
                # Header
                header = QLabel("Database Merge Conflicts Detected")
                header.setStyleSheet("font-size: 18px; font-weight: bold; color: #d32f2f; margin-bottom: 10px;")
                layout.addWidget(header)
                
                # Description
                desc = QLabel(
                    f"Found {len(self.conflicts)} conflicts between databases.\n"
                    "Please choose how to resolve each conflict:"
                )
                desc.setStyleSheet("margin-bottom: 15px;")
                layout.addWidget(desc)
                
                # Conflicts table
                self.table = QTableWidget(len(self.conflicts), 4)
                self.table.setHorizontalHeaderLabels([
                    "Conflict Type", "Details", "Source Data", "Resolution"
                ])
                
                # Setup table contents
                for i, conflict in enumerate(self.conflicts):
                    # Conflict type
                    conflict_type = conflict.get('type', 'unknown').title()
                    self.table.setItem(i, 0, QTableWidgetItem(conflict_type))
                    
                    # Details
                    if conflict_type.lower() == 'equipment':
                        details = f"BA Number: {conflict.get('ba_number', 'Unknown')}"
                    else:
                        details = conflict.get('description', 'Conflict detected')
                    self.table.setItem(i, 1, QTableWidgetItem(details))
                    
                    # Source data preview
                    source_preview = self.get_conflict_preview(conflict)
                    self.table.setItem(i, 2, QTableWidgetItem(source_preview))
                    
                    # Resolution options
                    resolution_widget = self.create_resolution_widget(i, conflict)
                    self.table.setCellWidget(i, 3, resolution_widget)
                
                # Adjust column widths
                header = self.table.horizontalHeader()
                if header:
                    header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
                    header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
                    header.setSectionResizeMode(2, QHeaderView.Stretch)
                    header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
                
                self.table.setSelectionBehavior(QTableWidget.SelectRows)
                layout.addWidget(self.table)
                
                # Buttons
                button_layout = QHBoxLayout()
                
                # Auto-resolve buttons
                auto_group = QGroupBox("Quick Actions")
                auto_layout = QHBoxLayout(auto_group)
                
                keep_source_btn = QPushButton("Keep All Source")
                keep_source_btn.setToolTip("Resolve all conflicts by keeping source database data")
                keep_source_btn.clicked.connect(self.keep_all_source)
                auto_layout.addWidget(keep_source_btn)
                
                keep_target_btn = QPushButton("Keep All Current")
                keep_target_btn.setToolTip("Resolve all conflicts by keeping current database data")
                keep_target_btn.clicked.connect(self.keep_all_target)
                auto_layout.addWidget(keep_target_btn)
                
                layout.addWidget(auto_group)
                
                button_layout.addStretch()
                
                cancel_btn = QPushButton("Cancel Merge")
                cancel_btn.clicked.connect(self.reject)
                button_layout.addWidget(cancel_btn)
                
                proceed_btn = QPushButton("Proceed with Merge")
                proceed_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4caf50;
                        color: white;
                        font-weight: bold;
                        padding: 8px 16px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #388e3c;
                    }
                """)
                proceed_btn.clicked.connect(self.accept)
                button_layout.addWidget(proceed_btn)
                
                layout.addLayout(button_layout)
            
            def get_conflict_preview(self, conflict):
                """Get a preview string for conflict data."""
                if conflict.get('type') == 'equipment':
                    source_data = conflict.get('source_data', {})
                    return f"Make: {source_data.get('make_and_type', 'N/A')}, KMs: {source_data.get('meterage_kms', 0)}"
                else:
                    return "Data conflict detected"
            
            def create_resolution_widget(self, row, conflict):
                """Create simple resolve button for a conflict."""
                from PyQt5.QtWidgets import QFrame, QPushButton
                from PyQt5.QtCore import Qt
                
                widget = QFrame()
                layout = QVBoxLayout(widget)
                layout.setContentsMargins(5, 5, 5, 5)
                
                # Simple resolve button with clear styling
                resolve_button = QPushButton("🔧 Resolve")
                resolve_button.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
                QPushButton:pressed {
                    background-color: #004085;
                }
                """)
                resolve_button.setMinimumHeight(35)
                
                # Capture the correct dialog reference for the lambda
                dialog_ref = self
                resolve_button.clicked.connect(lambda: dialog_ref.open_resolve_dialog(row, conflict))
                
                layout.addWidget(resolve_button)
                
                # Store default resolution (keep source)
                self.resolutions[row] = 'source'
                
                return widget
            
            def open_resolve_dialog(self, row, conflict):
                """Open a clear resolution dialog for a conflict."""
                from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
                
                class ResolveConflictDialog(QDialog):
                    def __init__(self, conflict, parent=None, parent_dialog=None):
                        super().__init__(parent)
                        self.conflict = conflict
                        self.resolution = 'source'  # Default
                        self.parent_dialog = parent_dialog  # Store reference to ConflictResolutionDialog
                        self.setup_ui()
                    
                    def setup_ui(self):
                        self.setWindowTitle("Resolve Data Conflict")
                        self.setFixedSize(500, 350)
                        self.setModal(True)
                        
                        layout = QVBoxLayout(self)
                        
                        # Header
                        header = QLabel(f"🔧 Conflict Resolution")
                        header.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
                        layout.addWidget(header)
                        
                        # Conflict info
                        info_text = f"BA Number: {self.conflict.get('ba_number', 'Unknown')}\n"
                        info_text += f"Conflict Type: {self.conflict.get('type', 'Equipment').title()}"
                        
                        info_label = QLabel(info_text)
                        info_label.setStyleSheet("background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;")
                        layout.addWidget(info_label)
                        
                        # Options frame
                        options_frame = QFrame()
                        options_layout = QVBoxLayout(options_frame)
                        
                        # Option 1: Keep Source
                        source_button = QPushButton("📥 Keep Source Data (from imported file)")
                        source_button.setStyleSheet("""
                        QPushButton {
                            background-color: #28a745;
                            color: white;
                            border: none;
                            padding: 15px;
                            border-radius: 5px;
                            font-weight: bold;
                            font-size: 13px;
                            text-align: left;
                            margin-bottom: 10px;
                        }
                        QPushButton:hover {
                            background-color: #218838;
                        }
                        """)
                        source_button.clicked.connect(lambda: self.set_resolution('source'))
                        options_layout.addWidget(source_button)
                        
                        # Option 2: Keep Current
                        current_button = QPushButton("🏠 Keep Current Data (in existing database)")
                        current_button.setStyleSheet("""
                        QPushButton {
                            background-color: #6c757d;
                            color: white;
                            border: none;
                            padding: 15px;
                            border-radius: 5px;
                            font-weight: bold;
                            font-size: 13px;
                            text-align: left;
                            margin-bottom: 10px;
                        }
                        QPushButton:hover {
                            background-color: #5a6268;
                        }
                        """)
                        current_button.clicked.connect(lambda: self.set_resolution('target'))
                        options_layout.addWidget(current_button)
                        
                        # Store button references for visual feedback
                        self.source_button = source_button
                        self.current_button = current_button
                        
                        layout.addWidget(options_frame)
                        
                        # Buttons
                        button_layout = QHBoxLayout()
                        
                        cancel_button = QPushButton("Cancel")
                        cancel_button.clicked.connect(self.reject)
                        button_layout.addWidget(cancel_button)
                        
                        button_layout.addStretch()
                        
                        ok_button = QPushButton("OK")
                        ok_button.setStyleSheet("background-color: #007bff; color: white; padding: 8px 20px; border-radius: 4px;")
                        ok_button.clicked.connect(self.accept)
                        button_layout.addWidget(ok_button)
                        
                        layout.addLayout(button_layout)
                    
                    def set_resolution(self, resolution):
                        self.resolution = resolution
                        
                        # Reset both buttons to normal style first
                        self.source_button.setStyleSheet("""
                        QPushButton {
                            background-color: #28a745;
                            color: white;
                            border: none;
                            padding: 15px;
                            border-radius: 5px;
                            font-weight: bold;
                            font-size: 13px;
                            text-align: left;
                            margin-bottom: 10px;
                        }
                        QPushButton:hover {
                            background-color: #218838;
                        }
                        """)
                        
                        self.current_button.setStyleSheet("""
                        QPushButton {
                            background-color: #6c757d;
                            color: white;
                            border: none;
                            padding: 15px;
                            border-radius: 5px;
                            font-weight: bold;
                            font-size: 13px;
                            text-align: left;
                            margin-bottom: 10px;
                        }
                        QPushButton:hover {
                            background-color: #5a6268;
                        }
                        """)
                        
                        # Highlight the selected option
                        if resolution == 'source':
                            self.source_button.setStyleSheet("""
                            QPushButton {
                                background-color: #28a745;
                                color: white;
                                border: 3px solid #155724;
                                padding: 15px;
                                border-radius: 5px;
                                font-weight: bold;
                                font-size: 13px;
                                text-align: left;
                                margin-bottom: 10px;
                            }
                            QPushButton:hover {
                                background-color: #218838;
                            }
                            """)
                            self.source_button.setText("✅ Keep Source Data (SELECTED)")
                            self.current_button.setText("🏠 Keep Current Data (in existing database)")
                        else:
                            self.current_button.setStyleSheet("""
                            QPushButton {
                                background-color: #6c757d;
                                color: white;
                                border: 3px solid #343a40;
                                padding: 15px;
                                border-radius: 5px;
                                font-weight: bold;
                                font-size: 13px;
                                text-align: left;
                                margin-bottom: 10px;
                            }
                            QPushButton:hover {
                                background-color: #5a6268;
                            }
                            """)
                            self.current_button.setText("✅ Keep Current Data (SELECTED)")
                            self.source_button.setText("📥 Keep Source Data (from imported file)")
                
                dialog = ResolveConflictDialog(conflict, self, parent_dialog=self)
                if dialog.exec_() == QDialog.Accepted:
                    # Update the resolution in the data structure
                    self.resolutions[row] = dialog.resolution
                    # Update the resolve button directly here where we have correct scope
                    try:
                        from PyQt5.QtGui import QColor
                        # Get the resolve button widget
                        widget = self.table.cellWidget(row, 3)  # Resolution column is index 3
                        if widget:
                            resolve_button = widget.findChild(QPushButton)
                            if resolve_button:
                                if dialog.resolution == 'source':
                                    resolve_button.setText("✅ KEEPING SOURCE")
                                    resolve_button.setStyleSheet("""
                                    QPushButton {
                                        background-color: #28a745;
                                        color: white;
                                        border: 2px solid #1e7e34;
                                        padding: 8px 16px;
                                        border-radius: 4px;
                                        font-weight: bold;
                                        font-size: 11px;
                                    }
                                    QPushButton:hover {
                                        background-color: #218838;
                                        border: 2px solid #155724;
                                    }
                                    """)
                                else:
                                    resolve_button.setText("🏠 KEEPING CURRENT") 
                                    resolve_button.setStyleSheet("""
                                    QPushButton {
                                        background-color: #6c757d;
                                        color: white;
                                        border: 2px solid #495057;
                                        padding: 8px 16px;
                                        border-radius: 4px;
                                        font-weight: bold;
                                        font-size: 11px;
                                    }
                                    QPushButton:hover {
                                        background-color: #5a6268;
                                        border: 2px solid #343a40;
                                    }
                                    """)
                    except Exception as e:
                        logger.error(f"Error updating resolve button inline: {e}")
            
            def update_resolve_button_correctly(self, row, resolution):
                """Update the resolve button to show the selected resolution - correctly scoped."""
                try:
                    # Get the resolve button widget
                    widget = self.table.cellWidget(row, 3)  # Resolution column is index 3
                    if widget:
                        resolve_button = widget.findChild(QPushButton)
                        if resolve_button:
                            if resolution == 'source':
                                resolve_button.setText("✅ KEEPING SOURCE")
                                resolve_button.setStyleSheet("""
                                QPushButton {
                                    background-color: #28a745;
                                    color: white;
                                    border: 2px solid #1e7e34;
                                    padding: 8px 16px;
                                    border-radius: 4px;
                                    font-weight: bold;
                                    font-size: 11px;
                                }
                                QPushButton:hover {
                                    background-color: #218838;
                                    border: 2px solid #155724;
                                }
                                """)
                            else:
                                resolve_button.setText("🏠 KEEPING CURRENT") 
                                resolve_button.setStyleSheet("""
                                QPushButton {
                                    background-color: #6c757d;
                                    color: white;
                                    border: 2px solid #495057;
                                    padding: 8px 16px;
                                    border-radius: 4px;
                                    font-weight: bold;
                                    font-size: 11px;
                                }
                                QPushButton:hover {
                                    background-color: #5a6268;
                                    border: 2px solid #343a40;
                                }
                                """)
                except Exception as e:
                    logger.error(f"Error updating resolve button correctly: {e}")
                    # Continue silently to avoid breaking the dialog
                    
            def update_resolve_button(self, row, resolution):
                """Update the resolve button to show the selected resolution."""
                # This method is called from bulk actions - redirect to correct method
                self.update_resolve_button_correctly(row, resolution)
            
            def keep_all_source(self):
                """Set all conflicts to keep source data."""
                for row in self.resolutions.keys():
                    self.resolutions[row] = 'source'
                    self.update_resolve_button(row, 'source')
            
            def keep_all_target(self):
                """Set all conflicts to keep target data."""
                for row in self.resolutions.keys():
                    self.resolutions[row] = 'target'
                    self.update_resolve_button(row, 'target')
            
            def get_conflict_resolutions(self):
                """Get the resolution choices for all conflicts."""
                return dict(self.resolutions)
        
        # Show conflict resolution dialog
        dialog = ConflictResolutionDialog(conflicts, source_stats, self)
        if dialog.exec_() == QDialog.Accepted:
            resolutions = dialog.get_conflict_resolutions()
            
            # Convert row-based resolutions to BA number-based resolutions for database manager
            ba_resolutions = {}
            for row, resolution in resolutions.items():
                if row < len(conflicts):
                    conflict = conflicts[row]
                    ba_number = conflict.get('ba_number')
                    if ba_number:
                        # Convert 'source' to 'accept' and 'target' to 'skip' to match Excel import logic
                        if resolution == 'source':
                            ba_resolutions[ba_number] = 'accept'
                        else:
                            ba_resolutions[ba_number] = 'skip'
            
            QMessageBox.information(
                self, 
                "Conflicts Resolved", 
                f"Resolved {len(conflicts)} conflicts. Proceeding with merge..."
            )
            return ba_resolutions
        else:
            return None

    def restart_application(self):
        """Restart the application to reload database."""
        from PyQt5.QtWidgets import QApplication
        import sys
        import subprocess
        
        try:
            # Close current application
            QApplication.quit()
            
            # Start new instance
            if getattr(sys, 'frozen', False):
                # Running as executable
                subprocess.Popen([sys.executable])
            else:
                # Running as script
                subprocess.Popen([sys.executable, sys.argv[0]])
            
        except Exception as e:
            logger.error(f"Error restarting application: {e}")
            QMessageBox.information(
                self, 
                "Restart Required", 
                "Please manually restart the application to see the imported data."
            )

    def export_demand(self, demand_type):
        """Export demand data to PDF or Excel/CSV, with fiscal year selection for fluids."""
        if demand_type == 'fluid':
            # Get available fiscal years from DemandForecast
            from models import DemandForecast
            fiscal_years = DemandForecast.get_fiscal_years()
            fy_list = [fy['fiscal_year'] for fy in fiscal_years] if isinstance(fiscal_years, list) and fiscal_years else []
            if not fy_list:
                QMessageBox.warning(self, "No Data", "No fiscal years found for fluid demand export.")
                return
            fy, ok = QInputDialog.getItem(
                self,
                "Select Fiscal Year",
                "Choose fiscal year to export:",
                fy_list,
                0, False
            )
            if not ok:
                return
            # Ask user for export format
            format_choice, ok = QInputDialog.getItem(
                self,
                "Select Export Format",
                "Choose export format:",
                ["PDF", "Excel (XLSX)", "CSV"],
                0, False
            )
            if not ok:
                return
            if format_choice == "PDF":
                file_filter = "PDF Files (*.pdf)"
                default_ext = ".pdf"
            elif format_choice == "Excel (XLSX)":
                file_filter = "Excel Files (*.xlsx)"
                default_ext = ".xlsx"
            else:
                file_filter = "CSV Files (*.csv)"
                default_ext = ".csv"
            filename, _ = QFileDialog.getSaveFileName(
                self,
                f"Export Fluid Demand",
                f"fluid_demand_forecast{default_ext}",
                file_filter
            )
            if not filename:
                return
            if not filename.lower().endswith(default_ext):
                filename += default_ext
            if format_choice == "PDF":
                progress = QProgressDialog(
                    f"Exporting fluid demand data...",
                    "Cancel", 0, 0, self
                )
                progress.setWindowTitle("Exporting")
                progress.setWindowModality(Qt.WindowModality.WindowModal)
                progress.show()
                QTimer.singleShot(1000, lambda: self._perform_export('fluid', filename, progress, fiscal_year=fy))
            else:
                self._export_fluid_excel_csv(filename, format_choice, fiscal_year=fy)
        else:
            # Existing logic for other types (PDF only)
            filename, _ = QFileDialog.getSaveFileName(
                self,
                f"Export {demand_type.capitalize()} Demand",
                f"{demand_type}_demand_forecast.pdf",
                "PDF Files (*.pdf)"
            )
            if not filename:
                return
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'
            progress = QProgressDialog(
                f"Exporting {demand_type} demand data...",
                "Cancel", 0, 0, self
            )
            progress.setWindowTitle("Exporting")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()
            QTimer.singleShot(1000, lambda: self._perform_export(demand_type, filename, progress))

    def import_policy_docs_path(self):
        """Open dialog to set policy documents path."""
        try:
            from ui.dialogs import PolicyDocsPathDialog
            from database import get_config_value, set_config_value
            
            # Get current path from database
            current_path = get_config_value('policy_docs_path', '')
            
            # Open dialog
            dialog = PolicyDocsPathDialog(self, current_path)
            if dialog.exec_() == QDialog.Accepted:
                new_path = dialog.get_path()
                
                if new_path:
                    # Validate path exists
                    import os
                    if not os.path.exists(new_path):
                        reply = QMessageBox.question(
                            self,
                            "Path Not Found",
                            f"The specified path does not exist:\n{new_path}\n\n"
                            "Do you want to save it anyway? (You can create the file/folder later)",
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.No
                        )
                        if reply == QMessageBox.No:
                            return
                    
                    # Save path to database
                    if set_config_value('policy_docs_path', new_path):
                        QMessageBox.information(
                            self,
                            "Success",
                            f"Policy documents path has been saved:\n{new_path}\n\n"
                            "You can now use the 'Policy Docs' button in the policy management dialog."
                        )
                    else:
                        QMessageBox.critical(
                            self,
                            "Error",
                            "Failed to save policy documents path to database."
                        )
                else:
                    # Clear path if empty
                    set_config_value('policy_docs_path', '')
                    QMessageBox.information(
                        self,
                        "Path Cleared",
                        "Policy documents path has been cleared."
                    )
        except Exception as e:
            logger.error(f"Error importing policy docs path: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open policy docs path dialog:\n{str(e)}"
            )
    
    def _export_fluid_excel_csv(self, filename, format_choice, fiscal_year=None):
        """Export fluid demand data to Excel or CSV in the detailed format for a specific fiscal year."""
        import pandas as pd
        from models import DemandForecast, Fluid, Equipment
        records = DemandForecast.get_by_fiscal_year(str(fiscal_year)) if fiscal_year else []
        columns = [
            "S. No.", "Item", "AU", "Equipment", "No of Vehs/Eqpt held",
            "No of scale per year per Eqpt", "Addl 10% topping", "Total (a+b) (Ltrs)",
            "Total requirement (e*h)", "Remarks"
        ]
        data = []
        if isinstance(records, list):
            for idx, rec in enumerate(records, 1):
                # Get additional fluid data
                fluid = Fluid.get_by_id(rec.get('fluid_id')) if rec.get('fluid_id') else None
                
                # Format equipment display using BA number priority
                equipment_display = utils.format_equipment_display(rec)
                
                row = [
                    idx,
                    rec.get('fluid_type', 'NA'),
                    rec.get('accounting_unit', 'Ltr'),  # Use accounting_unit from joined query
                    equipment_display,  # Use formatted equipment display with BA number
                    1,  # Default units held (can be enhanced later if needed)
                    (fluid.get('capacity_ltrs_kg', 0) if isinstance(fluid, dict) and fluid else 0),
                    (fluid.get('addl_10_percent_top_up', 0) if isinstance(fluid, dict) and fluid else 0),
                    (float(fluid.get('capacity_ltrs_kg', 0)) + float(fluid.get('addl_10_percent_top_up', 0))) if isinstance(fluid, dict) and fluid and 'capacity_ltrs_kg' in fluid and 'addl_10_percent_top_up' in fluid else 0,
                    rec.get('total_requirement', 0),
                    rec.get('remarks', 'Auto-generated from maintenance completion')
                ]
                data.append(row)
        df = pd.DataFrame(data)
        df.columns = columns
        logger.debug(f"All normalized columns: {df.columns.tolist()}")
        if format_choice == "Excel (XLSX)":
            df.to_excel(filename, index=False)
        else:
            df.to_csv(filename, index=False)
        QMessageBox.information(self, "Export Complete", f"Fluid demand data has been exported to:\n{filename}")
    
    def _perform_export(self, demand_type, filename, progress, fiscal_year=None):
        """Generate a detailed PDF report for the specified demand type and fiscal year."""
        try:
            try:
                from reportlab.lib.pagesizes import letter, A4
                from reportlab.lib import colors
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
                from reportlab.lib.units import inch
                from reportlab.graphics.shapes import Drawing
                from reportlab.graphics.charts.barcharts import VerticalBarChart
                from reportlab.graphics.charts.piecharts import Pie
                from reportlab.graphics import renderPDF
            except ImportError as e:
                QMessageBox.critical(self, "Export Error", "The 'reportlab' package is not installed. Please install it using 'pip install reportlab' and try again.")
                if progress:
                    progress.cancel()
                return
            import io
            import random
            from datetime import datetime
            
            # Get data based on demand type and fiscal year
            data = self._get_demand_data(demand_type, fiscal_year=fiscal_year)
            
            # Create PDF document
            doc = SimpleDocTemplate(
                filename,
                pagesize=A4,
                rightMargin=72, leftMargin=72,
                topMargin=72, bottomMargin=72
            )
            
            # Container for the 'Flowable' objects
            elements = []
            styles = getSampleStyleSheet()
            
            # Add title
            title_style = ParagraphStyle(
                'Title',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=1  # Center aligned
            )
            
            # Add company logo if available
            try:
                logo_path = os.path.join(os.path.dirname(__file__), 'resources', 'logo.png')
                if os.path.exists(logo_path):
                    logo = Image(logo_path, width=1.5*inch, height=0.75*inch)
                    elements.append(logo)
            except Exception as e:
                logger.warning(f"Could not load logo: {e}")
            
            elements.append(Paragraph(f"{demand_type.upper()} DEMAND FORECAST REPORT", title_style))
            
            # Add report info
            info_style = ParagraphStyle(
                'Info',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=12
            )
            
            elements.extend([
                Paragraph(f"<b>Report Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", info_style),
                Paragraph(f"<b>Period:</b> {data.get('period', 'Fiscal Year 2024')}", info_style),
                Spacer(1, 24)
            ])
            
            # Add summary section
            summary_style = ParagraphStyle(
                'Summary',
                parent=styles['Normal'],
                fontSize=10,
                leading=14,
                spaceAfter=12
            )
            
            elements.extend([
                Paragraph("<b>SUMMARY</b>", styles['Heading2']),
                Paragraph(data.get('summary', 'No summary available.'), summary_style),
                Spacer(1, 12)
            ])
            
            # Add data table
            if 'table_data' in data and data['table_data']:
                elements.append(Paragraph("<b>DETAILED DEMAND</b>", styles['Heading2']))
                
                # Define a style for table headers and cells
                table_header_style = ParagraphStyle(
                    'TableHeader', parent=styles['Normal'], fontSize=8, alignment=1, leading=10, spaceAfter=0, spaceBefore=0, wordWrap='CJK')
                table_cell_style = ParagraphStyle(
                    'TableCell', parent=styles['Normal'], fontSize=8, alignment=1, leading=10, spaceAfter=0, spaceBefore=0, wordWrap='CJK')
                # Wrap header and cell text in Paragraphs for better wrapping
                table_data_wrapped = []
                for row_idx, row in enumerate(data['table_data']):
                    wrapped_row = []
                    for col_idx, cell in enumerate(row):
                        # Use header style for first row, cell style otherwise
                        style = table_header_style if row_idx == 0 else table_cell_style
                        # Convert cell to string and wrap in Paragraph
                        wrapped_row.append(Paragraph(str(cell), style))
                    table_data_wrapped.append(wrapped_row)
                # Define table style (must be before Table is created)
                table_style = [
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4472C4')),  # Header background
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),  # Header text color
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # Center align all cells
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),  # Header font
                    ('FONTSIZE', (0, 0), (-1, 0), 10),  # Header font size
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),  # Header padding
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),  # Table body background
                    ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey),  # Grid lines
                    ('FONTSIZE', (0, 1), (-1, -1), 9),  # Table body font size
                    ('PADDING', (0, 0), (-1, -1), 6),  # Cell padding
                ]
                # Set explicit column widths for the table to prevent overlap
                # Adjust these widths as needed (in points; 1 inch = 72 points)
                col_widths = [30, 40, 30, 90, 50, 60, 60, 60, 70, 60]
                table = Table(table_data_wrapped, colWidths=col_widths)
                table.setStyle(TableStyle(table_style))
                elements.append(table)
                elements.append(Spacer(1, 24))
            
            # Add charts if data is available
            if 'chart_data' in data and data['chart_data']:
                # Only add chart if there is at least one non-empty data series
                has_chart_data = False
                if isinstance(data['chart_data'], list):
                    # For bar chart: chart_data is a list of lists
                    if any(isinstance(row, list) and any(row) for row in data['chart_data']):
                        has_chart_data = True
                if has_chart_data:
                    elements.append(Paragraph("<b>DEMAND ANALYSIS</b>", styles['Heading2']))
                    elements.append(Spacer(1, 30))  # Increase space between header and chart
                    drawing = Drawing(400, 320)  # Increase height to accommodate value labels and rotated category labels
                    
                    # Use bar chart for all demand types (fluid, tyre, battery)
                    bc = VerticalBarChart()
                    bc.x = 50
                    bc.y = 60  # Increase bottom margin for rotated labels
                    bc.height = 150
                    bc.width = 300
                    bc.data = data['chart_data']
                    bc.categoryAxis.categoryNames = data.get('categories', [])
                    bc.valueAxis.valueMin = 0
                    
                    # Configure category axis labels to prevent overlapping
                    bc.categoryAxis.labels.angle = 315  # Use 315 degrees (equivalent to -45) for downward rotation
                    bc.categoryAxis.labels.boxAnchor = 'nw'  # Anchor at top-left
                    bc.categoryAxis.labels.dx = -5  # Small horizontal offset to the left
                    bc.categoryAxis.labels.dy = -15  # Larger vertical offset downward
                    bc.categoryAxis.labels.fontSize = 8  # Smaller font to fit better
                    bc.categoryAxis.labels.fontName = 'Helvetica'
                    
                    # Set different colors for different demand types
                    if demand_type == 'fluid':
                        bc.bars[0].fillColor = colors.HexColor('#4472C4')  # Blue
                    elif demand_type == 'tyre':
                        bc.bars[0].fillColor = colors.HexColor('#ED7D31')  # Orange
                    else:  # battery
                        bc.bars[0].fillColor = colors.HexColor('#A5A5A5')  # Gray
                    
                    drawing.add(bc)
                    
                    # Add value labels on top of bars
                    from reportlab.graphics.shapes import String
                    chart_values = data['chart_data'][0] if data['chart_data'] else []
                    categories = data.get('categories', [])
                    
                    if chart_values and categories:
                        # Calculate bar positions and add value labels
                        num_bars = len(chart_values)
                        if num_bars > 0:
                            bar_width = bc.width / num_bars
                            max_value = max(chart_values) if chart_values else 1
                            
                            for i, (value, category) in enumerate(zip(chart_values, categories)):
                                if value > 0:  # Only show labels for non-zero values
                                    # Calculate x position (center of each bar)
                                    x_pos = bc.x + (i * bar_width) + (bar_width / 2)
                                    
                                    # Calculate y position (top of bar + small offset)
                                    bar_height = (value / max_value) * bc.height if max_value > 0 else 0
                                    y_pos = bc.y + bar_height + 10  # 10 points above the bar
                                    
                                    # Add value label
                                    value_label = String(x_pos, y_pos, str(int(value)))
                                    value_label.textAnchor = 'middle'  # Center the text
                                    value_label.fontSize = 10
                                    value_label.fillColor = colors.black
                                    drawing.add(value_label)
                    
                    elements.append(drawing)
                    elements.append(Spacer(1, 24))
                else:
                    elements.append(Paragraph("<b>DEMAND ANALYSIS</b>", styles['Heading2']))
                    elements.append(Spacer(1, 12))  # Add space for no data case too
                    elements.append(Paragraph("No data available for chart.", styles['Italic']))
                    elements.append(Spacer(1, 24))
            
            # Add notes if any
            if 'notes' in data and data['notes']:
                elements.extend([
                    Paragraph("<b>NOTES</b>", styles['Heading3']),
                    Paragraph(data['notes'], styles['Italic']),
                    Spacer(1, 12)
                ])
            
            # Add footer
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Italic'],
                fontSize=8,
                textColor=colors.grey,
                alignment=1  # Center aligned
            )
            elements.append(Paragraph("Confidential - For internal use only", footer_style))
            
            # Build the PDF
            doc.build(elements)
            
            # Close progress dialog
            progress.close()
            
            # Show success message
            QMessageBox.information(
                self,
                "Export Complete",
                f"{demand_type.capitalize()} demand report has been exported to:\n{filename}"
            )
            
        except Exception as e:
            logger.error(f"Error in export process: {e}")
            progress.close()
            QMessageBox.critical(
                self,
                "Export Error",
                f"An error occurred during export:\n{str(e)}"
            )
    
    def _get_demand_data(self, demand_type, fiscal_year=None):
        """Retrieve demand data based on demand type and fiscal year."""
        from models import DemandForecast, TyreMaintenance, Equipment, Fluid
        from datetime import datetime
        if fiscal_year is None:
            fiscal_year = datetime.now().year
        period_label = f"Fiscal Year {fiscal_year}"

        if demand_type == 'fluid':
            # Fetch fluid demand forecast for the current fiscal year
            records = DemandForecast.get_by_fiscal_year(str(fiscal_year))
            # Use detailed columns as per sample image
            columns = [
                "S. No.", "Item", "AU", "Equipment", "No of Vehs/Eqpt held",
                "No of scale per year per Eqpt", "Addl 10% topping", "Total (a+b) (Ltrs)",
                "Total requirement (e*h)", "Remarks"
            ]
            table_data = [columns]
            summary = f"This report provides an overview of the fluid demand forecast for {period_label}."
            notes = "Note: Quantities are in liters. Service intervals are based on manufacturer recommendations."
            fluid_totals = {}
            
            # Ensure records is a list before iterating
            if not isinstance(records, list):
                records = []
            
            for idx, rec in enumerate(records, 1):
                # Get additional fluid data
                fluid = Fluid.get_by_id(rec.get('fluid_id')) if rec.get('fluid_id') else None
                
                # Format equipment display using BA number priority
                equipment_display = utils.format_equipment_display(rec)
                
                # Safely access fluid data with proper type checking
                capacity_ltrs = 0
                addl_top_up = 0
                if fluid and isinstance(fluid, dict):
                    capacity_ltrs = fluid.get('capacity_ltrs_kg', 0)
                    addl_top_up = fluid.get('addl_10_percent_top_up', 0)
                
                row = [
                    idx,
                    rec.get('fluid_type', 'NA'),
                    rec.get('accounting_unit', 'Ltr'),  # Use accounting_unit from joined query
                    equipment_display,  # Use formatted equipment display with BA number
                    1,  # Default units held (can be enhanced later if needed)
                    capacity_ltrs,
                    addl_top_up,
                    float(capacity_ltrs) + float(addl_top_up),
                    rec.get('total_requirement', 0),
                    rec.get('remarks', 'Auto-generated from maintenance completion')
                ]
                table_data.append(row)
                # Aggregate for chart - Group by grade instead of fluid_type
                fluid_grade = rec.get('grade') or rec.get('fluid_type', 'Unknown')
                qty = rec.get('total_requirement', 0)
                if fluid_grade not in fluid_totals:
                    fluid_totals[fluid_grade] = 0
                fluid_totals[fluid_grade] += qty
            categories = list(fluid_totals.keys())
            chart_data = [[fluid_totals[cat] for cat in categories]]
            return {
                'period': period_label,
                'summary': summary,
                'table_data': table_data,
                'chart_data': chart_data,
                'categories': categories,
                'notes': notes
            }
        elif demand_type == 'tyre':
            # Fetch tyre demand forecast for the current fiscal year
            records = TyreForecast.get_by_fiscal_year(str(fiscal_year))
            
            # Use detailed columns for tyre forecast
            columns = [
                "S. No.", "Equipment", "Tyre Type", "Qty Required", 
                "Total Requirement", "Remarks"
            ]
            table_data = [columns]
            summary = f"This report provides an overview of the tyre demand forecast for {period_label}."
            notes = "Note: Tyre requirements are based on equipment tyre wear and replacement schedules."
            
            # Ensure records is a list before iterating
            if not isinstance(records, list):
                records = []
                
            tyre_totals = {}
            for idx, rec in enumerate(records, 1):
                # Format equipment display using BA number priority
                equipment_display = utils.format_equipment_display(rec)
                
                row = [
                    idx,
                    equipment_display,
                    rec.get('tyre_type', 'Unknown'),
                    rec.get('quantity_required', 1),
                    rec.get('total_requirement', 0),
                    rec.get('remarks', 'Auto-generated from tyre maintenance')
                ]
                table_data.append(row)
                
                # Aggregate for chart
                tyre_type = rec.get('tyre_type', 'Unknown')
                qty = rec.get('total_requirement', 0)
                if tyre_type not in tyre_totals:
                    tyre_totals[tyre_type] = 0
                tyre_totals[tyre_type] += qty
            
            categories = list(tyre_totals.keys()) if tyre_totals else ['No Data']
            chart_data = [[tyre_totals[cat] for cat in categories]] if tyre_totals else [[0]]
            
            return {
                'period': period_label,
                'summary': summary,
                'table_data': table_data,
                'chart_data': chart_data,
                'categories': categories,
                'notes': notes
            }
        else:  # battery
            # Fetch battery demand forecast for the current fiscal year
            records = BatteryForecast.get_by_fiscal_year(str(fiscal_year))
            
            # Use detailed columns for battery forecast
            columns = [
                "S. No.", "Equipment", "Battery Type", "Voltage (V)", 
                "Qty Required", "Total Requirement", "Remarks"
            ]
            table_data = [columns]
            summary = f"This report provides an overview of the battery demand forecast for {period_label}."
            notes = "Note: Battery requirements are based on equipment battery life and replacement schedules."
            
            # Ensure records is a list before iterating
            if not isinstance(records, list):
                records = []
                
            battery_totals = {}
            for idx, rec in enumerate(records, 1):
                # Format equipment display using BA number priority
                equipment_display = utils.format_equipment_display(rec)
                
                row = [
                    idx,
                    equipment_display,
                    rec.get('battery_type', 'Unknown'),
                    f"{rec.get('voltage', 12)}V",
                    rec.get('quantity_required', 1),
                    rec.get('total_requirement', 0),
                    rec.get('remarks', 'Auto-generated from battery maintenance')
                ]
                table_data.append(row)
                
                # Aggregate for chart
                battery_type = rec.get('battery_type', 'Unknown')
                qty = rec.get('total_requirement', 0)
                if battery_type not in battery_totals:
                    battery_totals[battery_type] = 0
                battery_totals[battery_type] += qty
            
            categories = list(battery_totals.keys()) if battery_totals else ['No Data']
            chart_data = [[battery_totals[cat] for cat in categories]] if battery_totals else [[0]]
            
            return {
                'period': period_label,
                'summary': summary,
                'table_data': table_data,
                'chart_data': chart_data,
                'categories': categories,
                'notes': notes
            }

def show_splash_screen():
    """Show splash screen while application is loading."""
    try:
        # Import Qt here for scope access
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QPainter, QColor, QFont, QPixmap
        from PyQt5.QtWidgets import QSplashScreen
        
        # Try to load splash image, fall back to a simple splash screen
        splash_pix = None
        splash_image_path = os.path.join('resources', 'splash.png')
        
        if os.path.exists(splash_image_path):
            splash_pix = QPixmap(splash_image_path)
        else:
            # Create a simple splash screen if image not found
            
            # Create a 400x300 pixmap with a blue gradient background
            splash_pix = QPixmap(400, 300)
            splash_pix.fill(QColor(33, 150, 243))  # Blue 500
            
            # Draw application name
            painter = QPainter(splash_pix)
            font = QFont()
            font.setPointSize(24)
            font.setBold(True)
            painter.setFont(font)
            
            # Draw text with shadow
            text = config.APP_NAME
            text_rect = painter.boundingRect(splash_pix.rect(), Qt.AlignmentFlag.AlignCenter, text)
            
            # Shadow
            painter.setPen(QColor(0, 0, 0, 128))
            painter.drawText(text_rect.adjusted(2, 2, 2, 2), Qt.AlignmentFlag.AlignCenter, text)
            
            # Main text
            painter.setPen(Qt.GlobalColor.white)
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, text)
            
            # Version
            font.setPointSize(12)
            font.setBold(False)
            painter.setFont(font)
            version_text = f"Version {config.APP_VERSION}"
            version_rect = painter.boundingRect(splash_pix.rect(), Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignRight, version_text)
            version_rect.adjust(-10, -10, -10, -10)  # Add some padding
            
            # Shadow for version
            painter.setPen(QColor(0, 0, 0, 128))
            painter.drawText(version_rect.adjusted(1, 1, 1, 1), Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom, version_text)
            
            # Main version text
            painter.setPen(Qt.GlobalColor.white)
            painter.drawText(version_rect, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom, version_text)
            
            painter.end()
        
        # Create splash screen
        splash = QSplashScreen(splash_pix, Qt.WindowType.WindowStaysOnTopHint)
        # Type ignore for enum combination - works at runtime
        splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)  # type: ignore
        splash.setEnabled(False)
        
        return splash
    except Exception as e:
        logger.warning(f"Could not show splash screen: {e}")
        return None

def main():
    """Main entry point of the application."""
    # Set up logging - simplified for Python 3.13 compatibility
    # Create a user-writable location for log files
    try:
        # Use AppData folder on Windows for logs
        app_data_dir = os.path.join(os.environ.get('APPDATA', '.'), 'InventoryTracker')
        if not os.path.exists(app_data_dir):
            os.makedirs(app_data_dir)
        log_file = os.path.join(app_data_dir, 'inventory_app.log')
        
        # Simple format to avoid recursion issues in Python 3.13
        logging.basicConfig(
            level=logging.INFO,
            format='%(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file)
            ]
        )
        logger.info(f"Logging to: {log_file}")
    except Exception as e:
        # Fallback to console-only logging if file logging fails
        logging.basicConfig(
            level=logging.INFO,
            format='%(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        logger.warning(f"Failed to set up file logging: {e}. Using console logging only.")
    
    logger.info("Starting PROJECT-ALPHA Equipment Inventory Management System")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Platform: {platform.platform()}")

    # Initialize quality checks
    try:
        from code_quality_improvements import initialize_quality_checks
        if not initialize_quality_checks():
            logger.warning("Some quality checks failed, but continuing...")
    except ImportError:
        logger.debug("Code quality improvements module not available")

    # Set up matplotlib backend before creating QApplication
    import matplotlib
    matplotlib.use('Qt5Agg')  # Use Qt5Agg backend for matplotlib

    # Create application with performance optimizations
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Use Fusion style for consistent look across platforms

    # Enable high DPI pixmaps and scaling
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)

    # Set application information
    app.setApplicationName(config.APP_NAME)
    app.setApplicationVersion(config.APP_VERSION)
    app.setOrganizationName("Military Equipment Management")

    # Initialize database with proper error handling
    try:
        from database_ready import ensure_db_ready
        success, first_run, corruption_recovered, error_message = ensure_db_ready()
        if not success:
            logger.error(f"Database initialization failed: {error_message}")
            QMessageBox.critical(None, "Database Error", f"The application could not initialize the database.\n\nDetails: {error_message or 'Unknown error.'}")
            sys.exit(1)
        if corruption_recovered:
            QMessageBox.warning(None, "Database Corruption Detected", "The database file was corrupted and has been backed up. A new database has been created.\n\nIf you had important data, please contact support with the backup file.")
        if first_run:
            QMessageBox.information(None, "Welcome!", "The database has been created for the first time on this system. You can now start using the application.")

        # Initialize database connection pool
        import database
        database.init_db()
        logger.info("Database initialized successfully")

    except Exception as e:
        logger.exception("Failed to initialize database:")
        QMessageBox.critical(
            None,
            "Database Error",
            f"Failed to initialize database:\n{str(e)}\n\nCheck the log file for details."
        )
        sys.exit(1)
    
    # Show splash screen
    splash = None
    try:
        splash = show_splash_screen()
        if splash:
            splash.show()
            app.processEvents()
    except Exception as e:
        logger.warning(f"Error showing splash screen: {e}")
    
    # Create main window
    main_window = None
    try:
        main_window = MainWindow()
        
        def show_main_window():
            try:
                if main_window:
                    main_window.show()
            except Exception as e:
                logger.error(f"Error showing main window: {e}")
            finally:
                if splash:
                    splash.finish(main_window if main_window else None)
        
        # Use timer to show splash screen for a minimum time
        QTimer.singleShot(1000, show_main_window)
        
        # Run application
        exit_code = app.exec_()
        return exit_code
        
    except Exception as e:
        logger.exception("Unhandled exception in main:")
        QMessageBox.critical(
            None,
            "Fatal Error",
            f"An unexpected error occurred:\n{str(e)}\n\nCheck the log file for details."
        )
        return 1
    finally:
        # Clean up resources with enhanced cleanup
        try:
            from code_quality_improvements import cleanup_application_resources
            cleanup_application_resources()
        except ImportError:
            # Fallback cleanup
            try:
                import matplotlib.pyplot as plt
                plt.close('all')
            except:
                pass

        # Ensure splash screen is closed
        if splash:
            splash.deleteLater()

        # Ensure main window is closed
        if main_window:
            main_window.close()
            main_window.deleteLater()

        # Clean up database connections
        try:
            import database
            # Use the correct cleanup function name
            pool = database.get_connection_pool()
            if pool:
                pool.close_all()
            logger.info("Database connection pool cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up database connections: {e}")

        # Ensure application exits cleanly
        app.quit()

        # Ensure Python exits
        sys.exit(0)

if __name__ == "__main__":
    main()